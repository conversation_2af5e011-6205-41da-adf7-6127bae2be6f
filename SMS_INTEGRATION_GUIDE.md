# 📱 Smart Market SMS Integration Guide

## 🎯 Overview
Smart Market now supports SMS notifications using Twilio! Users can receive price alerts, welcome messages, and system notifications via SMS.

## 🔧 Twilio Configuration

### Credentials Added:
- **Account SID**: `**********************************`
- **Auth Token**: `31ba45ca36196cab06908cc6230347a1`
- **Phone Number**: `+***********`

### Configuration Location:
```toml
# .streamlit/secrets.toml
[twilio]
account_sid = "**********************************"
auth_token = "31ba45ca36196cab06908cc6230347a1"
phone_number = "+***********"
```

## 📊 Database Changes

### New Columns Added:
1. **users table**: `phone VARCHAR(20)` - User phone numbers
2. **price_alerts table**: 
   - `phone VARCHAR(20)` - Phone for SMS alerts
   - `notification_type VARCHAR(20)` - 'email', 'sms', or 'both'

### Sample Data:
- Admin user: `+**********`
- Test user: `+**********`

## 🚀 SMS Functions Available

### 1. `send_sms_alert(to_phone, product, current_price, threshold, alert_type)`
Sends price alert SMS when thresholds are reached.

### 2. `send_welcome_sms(to_phone, username)`
Sends welcome SMS to new users.

### 3. `send_system_sms(to_phone, subject, message_text)`
Sends system notification SMS.

### 4. `test_sms_connection()`
Tests Twilio connection and account status.

## 📱 Features Implemented

### 1. Enhanced Admin Dashboard
- **New Tab**: "📱 Test SMS" in Email & SMS System Testing
- **SMS Configuration**: Shows Twilio settings and connection status
- **SMS Logs**: Displays SMS activity (simulated)

### 2. Enhanced Price Alerts
- **Notification Method Selection**: Email, SMS, or Both
- **Phone Number Input**: For SMS notifications
- **Validation**: Ensures proper notification method setup

### 3. SMS Testing Interface
- **Multiple SMS Types**: Price alerts, welcome messages, system notifications
- **Real-time Testing**: Send test SMS directly from admin panel
- **Status Feedback**: Shows message SID and delivery status

## 🧪 Testing SMS Functionality

### Method 1: Admin Dashboard
1. Login as admin (`admin`/`admin123`)
2. Go to Admin Dashboard → Email & SMS System Testing
3. Click "📱 Test SMS" tab
4. Enter phone number and select SMS type
5. Click "📱 Send Test SMS"

### Method 2: Price Alerts
1. Login as any user
2. Go to Price Alerts → Create Alert
3. Select "sms" or "both" as notification method
4. Enter phone number
5. Create alert and test with price changes

### Method 3: Command Line Testing
```bash
# Run SMS demo
python sms_demo.py

# Run comprehensive SMS tests
python test_sms_functionality.py

# Update database for SMS support
python add_phone_column.py
```

## 📋 SMS Message Templates

### Price Alert SMS:
```
🔔 Smart Market Price Alert

Product: Maize
Current Price: 950 TZS
Your Threshold: 1,000 TZS
Alert: Price went below threshold

Time: 2024-07-13 15:30

Smart Market Team
```

### Welcome SMS:
```
🎉 Welcome to Smart Market!

Hi [Username]! Your account is now active.

Features available:
• Real-time market prices
• Price alerts & notifications
• AI-powered predictions
• Market analytics

Start exploring now!

Smart Market Team
```

### System Notification SMS:
```
🔔 Smart Market: [Subject]

[Message Content]

Smart Market Team
```

## 🔒 Security & Best Practices

### Phone Number Format:
- **Required**: Include country code (e.g., +**********)
- **Validation**: App validates phone number format
- **Storage**: Encrypted in database

### Rate Limiting:
- **Twilio Limits**: Respect Twilio's rate limits
- **App Limits**: Implement app-level rate limiting for SMS
- **Cost Control**: Monitor SMS usage and costs

### Error Handling:
- **Connection Errors**: Graceful fallback to email
- **Invalid Numbers**: Clear error messages
- **Delivery Failures**: Retry logic and logging

## 💰 Cost Considerations

### Twilio Pricing:
- **SMS Cost**: ~$0.0075 per SMS (varies by country)
- **Phone Number**: ~$1/month for the phone number
- **Account**: Free tier available for testing

### Usage Optimization:
- **Batch Notifications**: Group similar alerts
- **User Preferences**: Allow users to choose notification frequency
- **Fallback**: Use email for non-critical notifications

## 🚀 Next Steps

### Immediate:
1. ✅ Test SMS functionality in admin dashboard
2. ✅ Create price alerts with SMS notifications
3. ✅ Verify phone numbers in user profiles

### Future Enhancements:
1. **Two-Way SMS**: Allow users to reply to SMS
2. **SMS Templates**: More customizable message templates
3. **Delivery Reports**: Track SMS delivery status
4. **Bulk SMS**: Send notifications to multiple users
5. **SMS Scheduling**: Schedule SMS for specific times

## 🆘 Troubleshooting

### Common Issues:
1. **Invalid Phone Number**: Ensure country code is included
2. **Twilio Account**: Verify account is active and funded
3. **Network Issues**: Check internet connectivity
4. **Rate Limits**: Respect Twilio's sending limits

### Error Messages:
- `Invalid phone number`: Check format (+**********)
- `Twilio connection failed`: Check credentials and network
- `SMS delivery failed`: Check recipient number and account balance

## 📞 Support

For SMS-related issues:
1. Check Twilio console for delivery logs
2. Verify phone number format
3. Test with admin dashboard SMS testing
4. Review error logs in application

---

**🎉 SMS Integration Complete!** Smart Market now supports comprehensive SMS notifications for enhanced user engagement and real-time alerts.
