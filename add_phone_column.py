#!/usr/bin/env python3
"""
Script to add phone number column to users table for SMS notifications
"""

import psycopg2

def get_connection():
    """Get database connection"""
    return psycopg2.connect(
        host="localhost",
        port="5432",
        dbname="market",
        user="postgres",
        password="alfred"
    )

def add_phone_column():
    """Add phone number column to users table"""
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            
            # Check if phone column already exists
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='users' AND column_name='phone'
            """)
            
            if cur.fetchone():
                print("✅ Phone column already exists in users table")
                return True
            
            # Add phone column
            cur.execute("ALTER TABLE users ADD COLUMN phone VARCHAR(20)")
            conn.commit()
            
            print("✅ Phone column added to users table successfully")
            
            # Update existing users with sample phone numbers for testing
            cur.execute("UPDATE users SET phone = '+1234567890' WHERE username = 'admin'")
            cur.execute("UPDATE users SET phone = '+1987654321' WHERE username = 'user'")
            conn.commit()
            
            print("✅ Sample phone numbers added for existing users")
            return True
            
    except Exception as e:
        print(f"❌ Error adding phone column: {e}")
        return False

def add_phone_to_alerts():
    """Add phone number column to price_alerts table"""
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            
            # Check if phone column already exists in price_alerts
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='price_alerts' AND column_name='phone'
            """)
            
            if cur.fetchone():
                print("✅ Phone column already exists in price_alerts table")
                return True
            
            # Add phone column to price_alerts
            cur.execute("ALTER TABLE price_alerts ADD COLUMN phone VARCHAR(20)")
            conn.commit()
            
            print("✅ Phone column added to price_alerts table successfully")
            return True
            
    except Exception as e:
        print(f"❌ Error adding phone column to price_alerts: {e}")
        return False

def add_notification_type_column():
    """Add notification_type column to price_alerts table"""
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            
            # Check if notification_type column already exists
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='price_alerts' AND column_name='notification_type'
            """)
            
            if cur.fetchone():
                print("✅ Notification_type column already exists in price_alerts table")
                return True
            
            # Add notification_type column
            cur.execute("ALTER TABLE price_alerts ADD COLUMN notification_type VARCHAR(20) DEFAULT 'email'")
            conn.commit()
            
            print("✅ Notification_type column added to price_alerts table successfully")
            return True
            
    except Exception as e:
        print(f"❌ Error adding notification_type column: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Adding SMS Support to Smart Market Database")
    print("=" * 50)
    
    # Add phone column to users table
    print("\n1. Adding phone column to users table...")
    add_phone_column()
    
    # Add phone column to price_alerts table
    print("\n2. Adding phone column to price_alerts table...")
    add_phone_to_alerts()
    
    # Add notification type column
    print("\n3. Adding notification_type column to price_alerts table...")
    add_notification_type_column()
    
    print("\n" + "=" * 50)
    print("🎉 SMS support database updates complete!")
    print("\n📝 Changes made:")
    print("   - Added 'phone' column to users table")
    print("   - Added 'phone' column to price_alerts table")
    print("   - Added 'notification_type' column to price_alerts table")
    print("   - Sample phone numbers added for testing")
    print("\n💡 Users can now:")
    print("   - Add phone numbers to their profiles")
    print("   - Choose email or SMS notifications for price alerts")
    print("   - Receive SMS notifications via Twilio")

if __name__ == "__main__":
    main()
