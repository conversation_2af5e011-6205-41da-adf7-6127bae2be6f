import pandas as pd
import streamlit as st
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.pipeline import make_pipeline
import psycopg2
from psycopg2.extras import RealDictCursor
import bcrypt
from PIL import Image
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time
import pyarrow as pa
from twilio.rest import Client

def fix_arrow_display_issue(df):
    """Convert datetime columns to strings to avoid PyArrow conversion issues"""
    df_copy = df.copy()
    for col in df_copy.columns:
        if pd.api.types.is_datetime64_any_dtype(df_copy[col]):
            df_copy[col] = pd.to_datetime(df_copy[col]).dt.strftime('%Y-%m-%d %H:%M:%S')
        elif pd.api.types.is_object_dtype(df_copy[col]):
            # Try to convert object columns that might contain timestamps
            try:
                temp = pd.to_datetime(df_copy[col])
                if not temp.isna().all():  # If conversion was successful
                    df_copy[col] = temp.dt.strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                pass  # Keep original if conversion fails
    return df_copy

def set_custom_style():
    st.markdown("""
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        /* Global Styles */
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Main Layout */
        .block-container {
            max-width: 1200px !important;
            padding-top: 1rem;
            padding-bottom: 2rem;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        /* Hide Streamlit branding */
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        header {visibility: hidden;}

        /* Modern Sidebar */
        [data-testid="stSidebar"] {
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%) !important;
            border-right: none !important;
            box-shadow: 4px 0 20px rgba(0,0,0,0.1);
        }

        [data-testid="stSidebar"] > div:first-child {
            background: transparent !important;
        }

        /* Hide Streamlit sidebar elements */
        [data-testid="stSidebarHeader"] {
            display: none !important;
        }
        [data-testid="stSidebarCollapsedControl"] {
            display: none !important;
        }
        [data-testid="stLogoSpacer"] {
            display: none !important;
        }

        /* Sidebar Brand */
        .sidebar-brand h2 {
            color: #ffffff !important;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 700;
            font-size: 1.8rem;
            letter-spacing: -0.5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Sidebar User Panel */
        .user-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .user-panel .user-avatar {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 600;
            margin-right: 0.75rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .user-panel .user-info {
            flex: 1;
        }

        .user-panel .user-name {
            color: #ffffff !important;
            font-weight: 600;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .user-panel .user-role {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        /* Modern Navigation Menu */
        .nav-menu {
            margin-top: 1rem;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255,255,255,0.9) !important;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1) !important;
            color: #ffffff !important;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: rgba(255,255,255,0.2) !important;
            color: #ffffff !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .nav-icon {
            margin-right: 0.75rem;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        /* Modern Info Boxes */
        .info-box {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .info-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
        }

        .info-box:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .info-box-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .info-box-text {
            font-size: 0.9rem;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-box-number {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }
        /* Modern Card Styling */
        .card {
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.12);
        }

        .card-header {
            padding: 1.5rem 2rem 1rem 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid rgba(0,0,0,0.08);
            position: relative;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-body {
            padding: 2rem;
        }

        /* Modern Buttons */
        .stButton > button {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 12px !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600 !important;
            font-size: 0.9rem !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
            text-transform: none !important;
            letter-spacing: 0.5px !important;
        }

        .stButton > button:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%) !important;
        }

        .stButton > button:active {
            transform: translateY(0px) !important;
        }

        /* Form Inputs */
        .stTextInput > div > div > input {
            border-radius: 12px !important;
            border: 2px solid #e2e8f0 !important;
            padding: 0.75rem 1rem !important;
            font-size: 0.9rem !important;
            transition: all 0.3s ease !important;
            background: #ffffff !important;
        }

        .stTextInput > div > div > input:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        .stSelectbox > div > div > div {
            border-radius: 12px !important;
            border: 2px solid #e2e8f0 !important;
            background: #ffffff !important;
        }

        .stNumberInput > div > div > input {
            border-radius: 12px !important;
            border: 2px solid #e2e8f0 !important;
            background: #ffffff !important;
        }
        /* Modern Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Enhanced Tables */
        .dataframe {
            border-radius: 12px !important;
            overflow: hidden !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
            border: 1px solid rgba(0,0,0,0.05) !important;
        }

        /* Modern Tabs */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
            background: #f8fafc;
            padding: 8px;
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .stTabs [data-baseweb="tab"] {
            background: transparent !important;
            border-radius: 8px !important;
            color: #64748b !important;
            font-weight: 500 !important;
            padding: 12px 20px !important;
            transition: all 0.3s ease !important;
        }

        .stTabs [aria-selected="true"] {
            background: linear-gradient(135deg, #3b82f6, #1e40af) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
        }

        /* Modern Metrics */
        [data-testid="metric-container"] {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        [data-testid="metric-container"]:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        /* Success/Error Messages */
        .stSuccess {
            background: linear-gradient(135deg, #10b981, #059669) !important;
            color: white !important;
            border-radius: 12px !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
        }

        .stError {
            background: linear-gradient(135deg, #ef4444, #dc2626) !important;
            color: white !important;
            border-radius: 12px !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
        }

        .stWarning {
            background: linear-gradient(135deg, #f59e0b, #d97706) !important;
            color: white !important;
            border-radius: 12px !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
        }

        .stInfo {
            background: linear-gradient(135deg, #3b82f6, #1e40af) !important;
            color: white !important;
            border-radius: 12px !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
        }

        /* Modern Footer */
        .main-footer {
            text-align: center;
            color: #64748b;
            font-size: 0.9rem;
            margin-top: 3rem;
            padding: 2rem 0;
            border-top: 1px solid rgba(0,0,0,0.05);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px 20px 0 0;
        }

        /* Loading Spinner */
        .stSpinner > div {
            border-top-color: #3b82f6 !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .block-container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .info-box {
                padding: 1rem;
            }

            .card-body {
                padding: 1.5rem;
            }
        }
        </style>
    """, unsafe_allow_html=True)

def show_loading_animation(text="Loading..."):
    with st.spinner(text):
        # Simulate loading for demonstration
        time.sleep(0.5)

# DB Connection with improved error handling and timeout
def get_connection():
    try:
        return psycopg2.connect(
            host=st.secrets["database"]["host"],
            port=st.secrets["database"]["port"],
            dbname=st.secrets["database"]["dbname"],
            user=st.secrets["database"]["user"],
            password=st.secrets["database"]["password"],
            connect_timeout=10,  # 10 second timeout
            application_name="smart_market_app"
        )
    except psycopg2.OperationalError as e:
        st.error("❌ Database connection failed. Please check your connection.")
        raise e
    except Exception as e:
        st.error(f"❌ Database error: {str(e)}")
        raise e

def load_market_data():
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT id, date, product, price, location FROM market_data ORDER BY date DESC")
            data = pd.DataFrame(cur.fetchall(), columns=['ID', 'Date', 'Product', 'Price', 'Location'])
            
            # Properly convert date column to pandas datetime
            data['Date'] = pd.to_datetime(data['Date'], errors='coerce')
            
            # Drop rows where date conversion failed
            data = data.dropna(subset=['Date'])
            
            # For display purposes, keep as datetime for now
            # We'll convert to string only when needed for specific displays
            
            return data
    except Exception as e:
        st.error(f"Error loading market data: {e}")
        return pd.DataFrame(columns=['ID', 'Date', 'Product', 'Price', 'Location'])

# --- Helper: Get all users as DataFrame ---
def get_all_users():
    import pandas as pd
    with get_connection() as conn:
        cur = conn.cursor()
        cur.execute("SELECT username, email, role FROM users ORDER BY username")
        users = cur.fetchall()
    return pd.DataFrame(users, columns=["Username", "Email", "Role"])

# Pagination Helper
def paginate_dataframe(df, items_per_page=10, key_prefix="pagination"):
    # Remove 'ID' column for display
    display_df = df.drop(columns=['ID'], errors='ignore').copy()

    # --- DataTables-like controls ---
    # Search/filter
    search_query = st.text_input("🔍 Search", value="", key=f"{key_prefix}_search")
    if search_query:
        mask = np.column_stack([
            display_df[col].astype(str).str.contains(search_query, case=False, na=False)
            for col in display_df
        ])
        display_df = display_df.loc[mask.any(axis=1)]

    # Sorting
    sort_col = st.selectbox("Sort by", display_df.columns, key=f"{key_prefix}_sort_col")
    sort_asc = st.radio("Order", ["Ascending", "Descending"], horizontal=True, key=f"{key_prefix}_sort_order")
    display_df = display_df.sort_values(by=sort_col, ascending=(sort_asc == "Ascending"))

    # Pagination
    total_rows = len(display_df)
    total_pages = max(1, (total_rows + items_per_page - 1) // items_per_page)
    page = st.number_input("Page", min_value=1, max_value=total_pages, value=1, step=1, key=f"{key_prefix}_page")
    start_idx = (page - 1) * items_per_page
    end_idx = min(start_idx + items_per_page, total_rows)
    paged_df = display_df.iloc[start_idx:end_idx]

    # Column sorting indicators
    styled_cols = [
        f"{col} {'🔼' if col == sort_col and sort_asc == 'Ascending' else ('🔽' if col == sort_col else '')}"
        for col in display_df.columns
    ]
    paged_df.columns = styled_cols

    # Show table and controls
    st.markdown(f"""
        <div class="datatable-controls">
            <div class="datatable-search">
                <b>Search:</b> <input value="{search_query}" style="margin-left:0.5rem;" readonly>
            </div>
            <div class="datatable-pagination">
                <b>Page:</b> {page} / {total_pages}
            </div>
        </div>
    """, unsafe_allow_html=True)
    st.dataframe(paged_df, use_container_width=True)

# Email Alert
def send_email_alert(to_email, product, current_price, threshold):
    try:
        smtp_server = st.secrets["email"]["smtp_server"]
        smtp_port = st.secrets["email"]["smtp_port"]
        email_user = st.secrets["email"]["email_user"]
        email_password = st.secrets["email"]["email_password"]

        msg = MIMEMultipart()
        msg["From"] = email_user
        msg["To"] = to_email
        msg["Subject"] = f"\U0001F514 Price Alert for {product}"

        body = f"The price for **{product}** has dropped to **{current_price}**, below your alert threshold of {threshold}."
        msg.attach(MIMEText(body, "plain"))

        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(email_user, email_password)
            server.send_message(msg)

        return True
    except Exception as e:
        st.error(f"Error sending email: {e}")
        return False

# Database connection test function
def test_database_connection():
    """Test database connection and return status"""
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT 1")
            return True, "Database connection successful"
    except Exception as e:
        return False, f"Database connection failed: {str(e)}"

# Session Initialization with better error handling
def initialize_session():
    """Initialize session state variables"""
    session_keys = {
        "logged_in": False,
        "username": "",
        "role": "",
        "current_page": None,  # Will be set based on role after login
        "confirm_delete_user": {},
        "confirm_delete_feedback": {},
        "confirm_delete_product": {},
        "login_attempts": 0,
        "last_login_attempt": None
    }

    for key, default_value in session_keys.items():
        if key not in st.session_state:
            st.session_state[key] = default_value

# Initialize session
initialize_session()

# Authentication
def login():
    set_custom_style()

    # Modern login page layout
    st.markdown("""
        <div style='text-align: center; margin-bottom: 3rem;'>
            <h1 style='font-size: 3rem; font-weight: 700; background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                       -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                       background-clip: text; margin-bottom: 0.5rem;'>
                🏪 Smart Market
            </h1>
            <p style='font-size: 1.2rem; color: #64748b; font-weight: 500;'>
                Your intelligent companion for market price tracking and analysis
            </p>
        </div>
    """, unsafe_allow_html=True)

    col_features, col_form = st.columns([2, 1])

    with col_features:
        st.markdown("""
            <div style='background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        padding: 2.5rem; border-radius: 20px;
                        margin-right: 2rem;
                        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
                        border: 1px solid rgba(0,0,0,0.05); height: 100%;'>
                <h3 style='color: #1e293b; margin-top: 0; font-weight: 600; margin-bottom: 2rem;'>
                    ✨ Platform Features
                </h3>
                <div style='margin: 1.5rem 0;'>
                    <div style='display: flex; align-items: center; margin-bottom: 1.5rem;
                                padding: 1rem; background: rgba(255,255,255,0.7); border-radius: 12px;'>
                        <div style='background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;
                                    width: 48px; height: 48px; border-radius: 12px;
                                    display: flex; align-items: center; justify-content: center;
                                    margin-right: 1rem; font-size: 1.5rem;'>📊</div>
                        <div>
                            <div style='color: #1e293b; font-weight: 600; margin-bottom: 0.25rem;'>Real-time Price Tracking</div>
                            <div style='color: #64748b; font-size: 0.9rem;'>Monitor market prices across multiple locations</div>
                        </div>
                    </div>
                    <div style='display: flex; align-items: center; margin-bottom: 1.5rem;
                                padding: 1rem; background: rgba(255,255,255,0.7); border-radius: 12px;'>
                        <div style='background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;
                                    width: 48px; height: 48px; border-radius: 12px;
                                    display: flex; align-items: center; justify-content: center;
                                    margin-right: 1rem; font-size: 1.5rem;'>🔮</div>
                        <div>
                            <div style='color: #1e293b; font-weight: 600; margin-bottom: 0.25rem;'>AI-Powered Predictions</div>
                            <div style='color: #64748b; font-size: 0.9rem;'>Advanced ML models for future price forecasting</div>
                        </div>
                    </div>
                    <div style='display: flex; align-items: center; margin-bottom: 1.5rem;
                                padding: 1rem; background: rgba(255,255,255,0.7); border-radius: 12px;'>
                        <div style='background: linear-gradient(135deg, #06b6d4, #0891b2); color: white;
                                    width: 48px; height: 48px; border-radius: 12px;
                                    display: flex; align-items: center; justify-content: center;
                                    margin-right: 1rem; font-size: 1.5rem;'>🔔</div>
                        <div>
                            <div style='color: #1e293b; font-weight: 600; margin-bottom: 0.25rem;'>Smart Alerts</div>
                            <div style='color: #64748b; font-size: 0.9rem;'>Custom price alerts and notifications</div>
                        </div>
                    </div>
                    <div style='display: flex; align-items: center;
                                padding: 1rem; background: rgba(255,255,255,0.7); border-radius: 12px;'>
                        <div style='background: linear-gradient(135deg, #10b981, #059669); color: white;
                                    width: 48px; height: 48px; border-radius: 12px;
                                    display: flex; align-items: center; justify-content: center;
                                    margin-right: 1rem; font-size: 1.5rem;'>📈</div>
                        <div>
                            <div style='color: #1e293b; font-weight: 600; margin-bottom: 0.25rem;'>Comprehensive Analytics</div>
                            <div style='color: #64748b; font-size: 0.9rem;'>Detailed reports and market insights</div>
                        </div>
                    </div>
                </div>
            </div>
        """, unsafe_allow_html=True)

    with col_form:
        st.markdown("""
            <div style='background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                        padding: 2rem; border-radius: 20px;
                        margin-left: -1rem;
                        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
                        border: 1px solid rgba(0,0,0,0.05); height: 100%;'>
                <div style='text-align: center; margin-bottom: 2rem;'>
                    <h3 style='color: #1e293b; font-weight: 600; margin-bottom: 0.5rem;'>🔐 Sign In</h3>
                    <p style='color: #64748b; font-size: 0.9rem;'>Access your Smart Market dashboard</p>
                </div>
            </div>
        """, unsafe_allow_html=True)

        with st.container():
            st.markdown("<div style='margin-top: -2rem; padding: 0 2.5rem 2.5rem 2.5rem;'>", unsafe_allow_html=True)

            username = st.text_input(
                "👤 Username",
                placeholder="Enter your username",
                help="Enter your registered username"
            )
            password = st.text_input(
                "🔒 Password",
                type='password',
                placeholder="Enter your password",
                help="Enter your account password"
            )

            st.markdown("<br>", unsafe_allow_html=True)

            if st.button("🚀 Sign In", type="primary", use_container_width=True):
                if not username or not password:
                    st.error("❌ Please enter both username and password")
                else:
                    # Check rate limiting
                    current_time = time.time()
                    if st.session_state.last_login_attempt:
                        time_since_last = current_time - st.session_state.last_login_attempt
                        if time_since_last < 2:  # 2 second cooldown
                            st.warning("⏳ Please wait a moment before trying again.")
                            return

                    # Check login attempts
                    if st.session_state.login_attempts >= 5:
                        st.error("❌ Too many failed login attempts. Please refresh the page and try again.")
                        return

                    st.session_state.last_login_attempt = current_time

                    # Show loading state
                    with st.spinner("🔐 Authenticating..."):
                        try:
                            # Test database connection first
                            db_status, db_message = test_database_connection()
                            if not db_status:
                                st.error(f"❌ {db_message}")
                                return

                            # Use the improved connection function
                            with get_connection() as conn:
                                cur = conn.cursor(cursor_factory=RealDictCursor)
                                # Use more specific query to reduce data transfer
                                cur.execute("SELECT username, password, role FROM users WHERE username = %s", (username,))
                                user = cur.fetchone()

                                if user:
                                    # Verify password
                                    if bcrypt.checkpw(password.encode(), user['password'].encode()):
                                        # Reset login attempts on successful login
                                        st.session_state.login_attempts = 0

                                        # Set session state
                                        st.session_state.logged_in = True
                                        st.session_state.username = username
                                        st.session_state.role = user['role']

                                        # Set appropriate dashboard based on role
                                        if user['role'] == 'admin':
                                            st.session_state.current_page = "AdminDashboard"
                                            st.success("🎉 Welcome back Admin! Redirecting to Admin Dashboard...")
                                        else:
                                            st.session_state.current_page = "MarketTracker"
                                            st.success("🎉 Welcome back! Redirecting to Market Tracker...")

                                        # Clear any previous error states
                                        if 'login_error' in st.session_state:
                                            del st.session_state['login_error']

                                        time.sleep(0.5)  # Brief pause for user feedback
                                        st.rerun()
                                    else:
                                        st.session_state.login_attempts += 1
                                        st.session_state['login_error'] = "Invalid password"
                                        st.error("❌ Invalid username or password")
                                else:
                                    st.session_state.login_attempts += 1
                                    st.session_state['login_error'] = "User not found"
                                    st.error("❌ Invalid username or password")

                        except psycopg2.OperationalError as e:
                            st.error("❌ Database connection failed. Please check your internet connection and try again.")
                            if "timeout" in str(e).lower():
                                st.info("💡 The database connection timed out. This might be due to network issues.")
                        except psycopg2.Error as e:
                            st.error("❌ Database error occurred. Please try again.")
                            st.error(f"Database error: {str(e)}")
                        except Exception as e:
                            st.error("❌ An unexpected error occurred. Please try again.")
                            st.error(f"Error: {str(e)}")

                    # Show login attempts remaining
                    if st.session_state.login_attempts > 0:
                        remaining = 5 - st.session_state.login_attempts
                        if remaining > 0:
                            st.info(f"ℹ️ {remaining} login attempts remaining")

            st.markdown("</div>", unsafe_allow_html=True)

        # Database status indicator
        db_status, db_message = test_database_connection()
        if db_status:
            st.markdown("""
                <div style='text-align: center; margin-top: 1rem; padding: 1rem;
                            background: rgba(34, 197, 94, 0.05); border-radius: 12px;
                            border: 1px solid rgba(34, 197, 94, 0.1);'>
                    <p style='color: #22c55e; font-size: 0.8rem; margin: 0; font-weight: 500;'>
                        🟢 Database Connected
                    </p>
                </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
                <div style='text-align: center; margin-top: 1rem; padding: 1rem;
                            background: rgba(239, 68, 68, 0.05); border-radius: 12px;
                            border: 1px solid rgba(239, 68, 68, 0.1);'>
                    <p style='color: #ef4444; font-size: 0.8rem; margin: 0; font-weight: 500;'>
                        🔴 Database Connection Issue
                    </p>
                    <p style='color: #64748b; font-size: 0.7rem; margin: 0.5rem 0 0 0;'>
                        {db_message}
                    </p>
                </div>
            """, unsafe_allow_html=True)

        st.markdown("""
            <div style='text-align: center; margin-top: 2rem; padding: 1.5rem;
                        background: rgba(59, 130, 246, 0.05); border-radius: 12px;
                        border: 1px solid rgba(59, 130, 246, 0.1);'>
                <p style='color: #3b82f6; font-size: 0.9rem; margin: 0; font-weight: 500;'>
                    💡 Need access? Contact your administrator
                </p>
                <p style='color: #64748b; font-size: 0.8rem; margin: 0.5rem 0 0 0;'>
                    Demo credentials: admin/admin123 or user/user123
                </p>
            </div>
        """, unsafe_allow_html=True)

        # Troubleshooting section
        with st.expander("🔧 Troubleshooting Login Issues"):
            st.markdown("""
            **If you're experiencing login delays or failures:**

            1. **Check Database Status**: Look for the green/red indicator above
            2. **Wait Between Attempts**: There's a 2-second cooldown between login attempts
            3. **Check Credentials**: Ensure username and password are correct
            4. **Network Issues**: Slow internet can cause timeouts
            5. **Browser Issues**: Try refreshing the page or clearing cache

            **Common Solutions:**
            - Refresh the page if you see connection errors
            - Wait a few seconds between login attempts
            - Check if your internet connection is stable
            - Contact admin if database shows as disconnected

            **Rate Limiting:**
            - Maximum 5 login attempts before lockout
            - 2-second cooldown between attempts
            - Refresh page to reset after lockout
            """)

            if st.button("🔄 Test Database Connection"):
                with st.spinner("Testing connection..."):
                    status, message = test_database_connection()
                    if status:
                        st.success(f"✅ {message}")
                    else:
                        st.error(f"❌ {message}")

            if st.button("🔄 Reset Login Attempts"):
                st.session_state.login_attempts = 0
                st.session_state.last_login_attempt = None
                st.success("✅ Login attempts reset!")

def logout():
    st.session_state.logged_in = False
    st.session_state.username = ''
    st.session_state.role = ''
    st.session_state.current_page = None  # Reset current page
    st.toast("You have been logged out.", icon="✅")
    st.rerun()

# Modern Sidebar
def render_sidebar():
    st.sidebar.markdown("""
        <div class='sidebar-brand'>
            <h2>🏪 Smart Market</h2>
        </div>

        <div class="user-panel">
            <div style="display: flex; align-items: center;">
                <div class="user-avatar">
                    {0}
                </div>
                <div class="user-info">
                    <div class="user-name">{1}</div>
                    <div class="user-role">{2}</div>
                </div>
            </div>
        </div>
    """.format(
        st.session_state.username[0].upper(),
        st.session_state.username,
        st.session_state.role.capitalize()
    ), unsafe_allow_html=True)

    # Modern navigation menu with emoji icons
    menu_items = {
        'admin': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "⚙️", "label": "Admin Dashboard", "id": "AdminDashboard"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ],
        'user': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "🔔", "label": "Price Alerts", "id": "PriceAlerts"},
            {"icon": "💬", "label": "Feedback", "id": "Feedback"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ],
        'default': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "�", "label": "Price Alerts", "id": "PriceAlerts"},
            {"icon": "�💬", "label": "Feedback", "id": "Feedback"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ]
    }

    # Select menu items based on user role
    if st.session_state.role == 'admin':
        items = menu_items['admin']
    elif st.session_state.role == 'user':
        items = menu_items.get('user', menu_items['default'])
    else:
        items = menu_items['default']

    # Create modern navigation menu
    st.sidebar.markdown('<div class="nav-menu">', unsafe_allow_html=True)

    for item in items:
        if st.sidebar.button(
            f"{item['icon']} {item['label']}",
            key=f"nav_{item['id']}",
            use_container_width=True,
            help=item['label']
        ):
            # Update the current page in session state
            st.session_state.current_page = item['id']
            st.rerun()

    st.sidebar.markdown('</div>', unsafe_allow_html=True)

    # Return the current page from session state, with fallback based on role
    if st.session_state.current_page is None:
        if st.session_state.role == 'admin':
            st.session_state.current_page = "AdminDashboard"
        else:
            st.session_state.current_page = "MarketTracker"

    return st.session_state.current_page

# Admin Dashboard
def admin_dashboard():
    set_custom_style()

    # Modern admin dashboard header
    st.markdown("""
        <div style='text-align: center; margin-bottom: 2rem; padding: 2rem;
                    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
                    border-radius: 20px; color: white; box-shadow: 0 8px 32px rgba(124, 58, 237, 0.3);'>
            <h1 style='margin: 0; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;'>
                ⚙️ Admin Dashboard
            </h1>
            <p style='margin: 0; font-size: 1.1rem; opacity: 0.9;'>
                System administration and user management
            </p>
        </div>
    """, unsafe_allow_html=True)

    # Add some spacing and styling for tabs
    st.markdown("""
        <style>
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 20px;
        }
        .stTabs [data-baseweb="tab"] {
            height: 50px;
            padding: 0px 20px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: white;
            font-weight: 500;
            border: none;
        }
        .stTabs [aria-selected="true"] {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6) !important;
            color: white !important;
        }
        </style>
    """, unsafe_allow_html=True)

    # Create tabs for different admin sections
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "👥 User Management",
        "➕ Add New User",
        "💬 Feedback Management",
        "📦 Market Data Management",
        "📧 Email System Testing",
        "🔒 Security Testing"
    ])

    with tab1:
        st.markdown("### 👥 User Management")
        st.write("View, edit, and manage user accounts")
        admin_user_management()

    with tab2:
        st.markdown("### ➕ Add New User")
        st.write("Create new user accounts with roles and permissions")
        admin_add_user()

    with tab3:
        st.markdown("### 💬 Feedback Management")
        st.write("Review and manage user feedback and suggestions")
        admin_feedback_management()

    with tab4:
        st.markdown("### 📦 Market Data Management")
        st.write("Manage market data with CRUD operations and analytics")
        admin_market_data_management()

    with tab5:
        st.markdown("### 📧 Email System Testing")
        st.write("Test and configure email notifications and alerts")
        admin_email_testing()

    with tab6:
        st.markdown("### 🔒 Security Testing")
        st.write("Security validation, access control, and system testing")
        admin_security_testing()

def admin_user_management():
    st.subheader("👥 User Management")
    st.write("Manage system users and their permissions")

    # Initialize session state for delete confirmations
    if 'delete_user_confirm' not in st.session_state:
        st.session_state.delete_user_confirm = {}

    user_df = get_all_users()
    if not user_df.empty:
        st.markdown("### Current Users")

        # Search functionality
        search_term = st.text_input("🔍 Search users", placeholder="Search by username, email, or role...")

        if search_term:
            mask = user_df.apply(lambda row: row.astype(str).str.contains(search_term, case=False).any(), axis=1)
            filtered_df = user_df[mask]
        else:
            filtered_df = user_df

        if filtered_df.empty:
            st.info("No users found matching your search.")
        else:
            # Display users in a modern card layout
            for idx, user in filtered_df.iterrows():
                username = user['Username']
                email = user['Email']
                role = user['Role']

                # Create user card
                with st.container():
                    st.markdown(f"""
                        <div style='background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                                    padding: 1.5rem; border-radius: 16px; margin-bottom: 1rem;
                                    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                                    border: 1px solid rgba(0,0,0,0.05);'>
                            <div style='display: flex; align-items: center; justify-content: space-between;'>
                                <div style='display: flex; align-items: center;'>
                                    <div style='background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                                                color: white; width: 48px; height: 48px; border-radius: 12px;
                                                display: flex; align-items: center; justify-content: center;
                                                margin-right: 1rem; font-size: 1.2rem; font-weight: 600;'>
                                        {username[0].upper()}
                                    </div>
                                    <div>
                                        <div style='font-weight: 600; color: #1e293b; font-size: 1.1rem;'>{username}</div>
                                        <div style='color: #64748b; font-size: 0.9rem; margin: 0.25rem 0;'>{email}</div>
                                        <div style='display: inline-block; background: linear-gradient(135deg, #10b981, #059669);
                                                    color: white; padding: 0.25rem 0.75rem; border-radius: 20px;
                                                    font-size: 0.8rem; font-weight: 500; text-transform: uppercase;'>
                                            {role}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    """, unsafe_allow_html=True)

                    # Action buttons
                    col1, col2, col3 = st.columns([1, 1, 4])

                    with col1:
                        if st.button("✏️ Edit", key=f"edit_{username}", help=f"Edit {username}"):
                            st.session_state[f"edit_mode_{username}"] = True
                            st.rerun()

                    with col2:
                        # Prevent deleting the current user
                        if username == st.session_state.username:
                            st.button("🚫 Cannot Delete Self", key=f"nodelete_{username}", disabled=True)
                        else:
                            if st.button("🗑️ Delete", key=f"delete_{username}", help=f"Delete {username}"):
                                st.session_state.delete_user_confirm[username] = True

                    # Confirmation dialog
                    if st.session_state.delete_user_confirm.get(username, False):
                        st.warning(f"⚠️ Are you sure you want to delete user **{username}**? This action cannot be undone!")

                        col_confirm, col_cancel = st.columns(2)
                        with col_confirm:
                            if st.button("✅ Confirm Delete", key=f"confirm_delete_{username}", type="primary"):
                                try:
                                    with get_connection() as conn:
                                        cur = conn.cursor()
                                        cur.execute("DELETE FROM users WHERE username = %s", (username,))
                                        conn.commit()
                                        cur.close()

                                    st.success(f"🎉 User '{username}' has been deleted successfully!")
                                    st.session_state.delete_user_confirm[username] = False
                                    st.rerun()

                                except Exception as e:
                                    st.error(f"❌ Failed to delete user: {e}")

                        with col_cancel:
                            if st.button("❌ Cancel", key=f"cancel_delete_{username}"):
                                st.session_state.delete_user_confirm[username] = False
                                st.rerun()

                    # Edit user form
                    if st.session_state.get(f"edit_mode_{username}", False):
                        st.markdown("### Edit User Details")
                        with st.form(f"edit_user_form_{username}"):
                            new_email = st.text_input("Email", value=email, key=f"edit_email_{username}")
                            new_role = st.selectbox("Role", ['admin', 'user'],
                                                  index=0 if role == 'admin' else 1,
                                                  key=f"edit_role_{username}")

                            col_save, col_cancel = st.columns(2)
                            with col_save:
                                if st.form_submit_button("💾 Save Changes", type="primary"):
                                    try:
                                        with get_connection() as conn:
                                            cur = conn.cursor()
                                            cur.execute("""
                                                UPDATE users
                                                SET email = %s, role = %s
                                                WHERE username = %s
                                            """, (new_email, new_role, username))
                                            conn.commit()

                                        st.success(f"✅ User '{username}' updated successfully!")
                                        st.session_state[f"edit_mode_{username}"] = False
                                        st.rerun()

                                    except Exception as e:
                                        st.error(f"❌ Failed to update user: {e}")

                            with col_cancel:
                                if st.form_submit_button("❌ Cancel"):
                                    st.session_state[f"edit_mode_{username}"] = False
                                    st.rerun()

                    st.markdown("---")

        # Summary
        st.markdown(f"""
            <div style='background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
                        color: white; padding: 1rem; border-radius: 12px; margin-top: 1rem;'>
                <strong>📊 Summary:</strong> {len(filtered_df)} users displayed
                {f"(filtered from {len(user_df)} total)" if search_term else ""}
            </div>
        """, unsafe_allow_html=True)

    else:
        st.info("No users found in the system.")

def admin_add_user():
    st.subheader("Add New User")
    st.write(f"Welcome, **{st.session_state.username}**! You can add new users to the system here.")

    # Use a form to prevent reruns/jumps on input change
    with st.form("add_user_form", clear_on_submit=True):
        st.write("**Fill out the form below:**")

        new_user = st.text_input("Username", help="Enter a unique username")
        new_email = st.text_input("Email Address", help="Enter a valid email address")
        new_password = st.text_input("Password", type='password', help="Enter a secure password")
        role = st.selectbox("Role", ['admin', 'user'], help="Select user role")

        submitted = st.form_submit_button("Add User", type="primary")

        if submitted:
            if not new_user or not new_email or not new_password:
                st.warning("❌ All fields are required to add a new user.")
            elif not new_user.strip() or not new_email.strip() or not new_password.strip():
                st.warning("❌ All fields must contain valid data (no empty spaces).")
            else:
                try:
                    # Clean the input data
                    new_user = new_user.strip()
                    new_email = new_email.strip()

                    with get_connection() as conn:
                        cur = conn.cursor()
                        try:
                            # Check if user already exists
                            cur.execute("SELECT username FROM users WHERE username = %s", (new_user,))
                            existing_user = cur.fetchone()

                            if existing_user:
                                st.warning(f"❌ User '{new_user}' already exists!")
                            else:
                                # Hash the password
                                hashed_pw = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt()).decode()

                                # Insert new user
                                cur.execute(
                                    "INSERT INTO users (username, password, email, role) VALUES (%s, %s, %s, %s)",
                                    (new_user, hashed_pw, new_email, role)
                                )

                                # Commit the transaction
                                conn.commit()

                                st.success(f"🎉 User '{new_user}' added successfully!")
                                st.info("User added successfully! You can view them in User Management.")

                                # Don't modify session state after widget instantiation
                                # Just show success and let user navigate manually
                                time.sleep(1)  # Brief pause for user to see success message

                        except Exception as db_error:
                            # Rollback on error
                            conn.rollback()
                            st.error(f"❌ Database error: {db_error}")
                            import traceback
                            st.code(traceback.format_exc())
                            raise db_error
                        finally:
                            # Always close the cursor
                            cur.close()

                except Exception as e:
                    st.error(f"❌ Failed to add user: {e}")
                    import traceback
                    st.code(traceback.format_exc())

def admin_feedback_management():
    st.subheader("User Feedback")
    with get_connection() as conn:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("SELECT id, username, feedback, timestamp FROM feedback ORDER BY timestamp DESC")
        feedbacks = cur.fetchall()

    if feedbacks:
        for entry in feedbacks:
            cols = st.columns([5, 1])
            with cols[0]:
                st.markdown(f"**{entry['username']}** at {entry['timestamp']} said:")
                st.write(f"📣 _{entry['feedback']}_")
            if cols[1].button("❌", key=f"del_feedback_{entry['id']}"):
                st.session_state.confirm_delete_feedback[entry['id']] = True
            if st.session_state.confirm_delete_feedback.get(entry['id'], False):
                if st.checkbox("Confirm delete feedback?", key=f"confirm_feedback_{entry['id']}"):
                    with get_connection() as conn:
                        cur = conn.cursor()
                        cur.execute("DELETE FROM feedback WHERE id = %s", (entry['id'],))
                        conn.commit()
                        st.toast("Feedback deleted.", icon="✅")
                        st.rerun()
    else:
        st.info("No feedback submitted yet.")

def admin_market_data_management():
    st.subheader("📦 Market Data Management")
    st.write("Manage market data with full CRUD operations, import/export, and validation")

    # Create tabs for different operations
    tab1, tab2, tab3, tab4 = st.tabs(["📋 View Data", "➕ Add Data", "📤 Import/Export", "📊 Statistics"])

    with tab1:
        st.subheader("Market Data Records")

        # Load data
        data = load_market_data()

        if not data.empty:
            # Search and filter options
            col1, col2, col3 = st.columns(3)
            with col1:
                search_product = st.selectbox("Filter by Product", ["All"] + list(data['Product'].unique()))
            with col2:
                search_location = st.selectbox("Filter by Location", ["All"] + list(data['Location'].unique()))
            with col3:
                date_range = st.date_input("Date Range", value=[data['Date'].min().date(), data['Date'].max().date()])

            # Apply filters
            filtered_data = data.copy()
            if search_product != "All":
                filtered_data = filtered_data[filtered_data['Product'] == search_product]
            if search_location != "All":
                filtered_data = filtered_data[filtered_data['Location'] == search_location]
            if len(date_range) == 2:
                filtered_data = filtered_data[
                    (filtered_data['Date'].dt.date >= date_range[0]) &
                    (filtered_data['Date'].dt.date <= date_range[1])
                ]

            st.write(f"Showing {len(filtered_data)} of {len(data)} records")

            # Pagination
            items_per_page = 10
            total_pages = (len(filtered_data) - 1) // items_per_page + 1

            if total_pages > 1:
                page = st.selectbox("Page", range(1, total_pages + 1))
                start_idx = (page - 1) * items_per_page
                end_idx = start_idx + items_per_page
                page_data = filtered_data.iloc[start_idx:end_idx]
            else:
                page_data = filtered_data

            # Display data with edit/delete options
            for idx, row in page_data.iterrows():
                with st.container():
                    col1, col2, col3, col4, col5, col6 = st.columns([1, 2, 2, 2, 2, 2])

                    with col1:
                        st.write(f"**ID:** {row['ID']}")
                    with col2:
                        st.write(f"**Date:** {row['Date'].strftime('%Y-%m-%d')}")
                    with col3:
                        st.write(f"**Product:** {row['Product']}")
                    with col4:
                        st.write(f"**Price:** {row['Price']:,.0f} TZS")
                    with col5:
                        st.write(f"**Location:** {row['Location']}")
                    with col6:
                        col_edit, col_delete = st.columns(2)
                        with col_edit:
                            if st.button("✏️", key=f"edit_{row['ID']}", help="Edit record"):
                                st.session_state[f"edit_mode_{row['ID']}"] = True
                                st.rerun()
                        with col_delete:
                            if st.button("🗑️", key=f"delete_{row['ID']}", help="Delete record"):
                                st.session_state[f"delete_confirm_{row['ID']}"] = True
                                st.rerun()

                    # Edit form
                    if st.session_state.get(f"edit_mode_{row['ID']}", False):
                        with st.form(f"edit_form_{row['ID']}"):
                            st.write("**Edit Record**")
                            edit_col1, edit_col2 = st.columns(2)
                            with edit_col1:
                                new_date = st.date_input("Date", value=row['Date'].date(), key=f"edit_date_{row['ID']}")
                                new_product = st.text_input("Product", value=row['Product'], key=f"edit_product_{row['ID']}")
                            with edit_col2:
                                new_price = st.number_input("Price (TZS)", value=float(row['Price']), min_value=0.0, key=f"edit_price_{row['ID']}")
                                new_location = st.text_input("Location", value=row['Location'], key=f"edit_location_{row['ID']}")

                            col_save, col_cancel = st.columns(2)
                            with col_save:
                                if st.form_submit_button("💾 Save", type="primary"):
                                    try:
                                        with get_connection() as conn:
                                            cur = conn.cursor()
                                            cur.execute("""
                                                UPDATE market_data
                                                SET date = %s, product = %s, price = %s, location = %s
                                                WHERE id = %s
                                            """, (new_date, new_product, new_price, new_location, row['ID']))
                                            conn.commit()
                                        st.success("✅ Record updated successfully!")
                                        st.session_state[f"edit_mode_{row['ID']}"] = False
                                        st.rerun()
                                    except Exception as e:
                                        st.error(f"❌ Error updating record: {e}")

                            with col_cancel:
                                if st.form_submit_button("❌ Cancel"):
                                    st.session_state[f"edit_mode_{row['ID']}"] = False
                                    st.rerun()

                    # Delete confirmation
                    if st.session_state.get(f"delete_confirm_{row['ID']}", False):
                        st.warning(f"⚠️ Are you sure you want to delete this record?")
                        col_confirm, col_cancel = st.columns(2)
                        with col_confirm:
                            if st.button("✅ Confirm Delete", key=f"confirm_del_{row['ID']}", type="primary"):
                                try:
                                    with get_connection() as conn:
                                        cur = conn.cursor()
                                        cur.execute("DELETE FROM market_data WHERE id = %s", (row['ID'],))
                                        conn.commit()
                                    st.success("✅ Record deleted successfully!")
                                    st.session_state[f"delete_confirm_{row['ID']}"] = False
                                    st.rerun()
                                except Exception as e:
                                    st.error(f"❌ Error deleting record: {e}")
                        with col_cancel:
                            if st.button("❌ Cancel Delete", key=f"cancel_del_{row['ID']}"):
                                st.session_state[f"delete_confirm_{row['ID']}"] = False
                                st.rerun()

                    st.divider()
        else:
            st.info("No market data available.")

    with tab2:
        st.subheader("Add New Market Data")

        with st.form("add_market_data_form"):
            col1, col2 = st.columns(2)
            with col1:
                new_date = st.date_input("Date", value=pd.Timestamp.now().date())
                new_product = st.text_input("Product", placeholder="e.g., Maize, Rice, Beans")
            with col2:
                new_price = st.number_input("Price (TZS)", min_value=0.0, value=1000.0, step=10.0)
                new_location = st.text_input("Location", placeholder="e.g., Dar es Salaam, Mwanza")

            if st.form_submit_button("➕ Add Record", type="primary"):
                if new_product and new_location and new_price > 0:
                    try:
                        with get_connection() as conn:
                            cur = conn.cursor()
                            cur.execute("""
                                INSERT INTO market_data (date, product, price, location)
                                VALUES (%s, %s, %s, %s)
                            """, (new_date, new_product, new_price, new_location))
                            conn.commit()
                        st.success("✅ Market data added successfully!")
                        st.rerun()
                    except Exception as e:
                        st.error(f"❌ Error adding record: {e}")
                else:
                    st.warning("⚠️ Please fill in all fields with valid data")

    with tab3:
        st.subheader("Import/Export Data")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**📤 Export Data**")
            data = load_market_data()
            if not data.empty:
                # Convert to CSV
                csv_data = data.to_csv(index=False)
                st.download_button(
                    label="📥 Download CSV",
                    data=csv_data,
                    file_name=f"market_data_{pd.Timestamp.now().strftime('%Y%m%d')}.csv",
                    mime="text/csv"
                )

                # Show preview
                st.write("**Data Preview:**")
                st.dataframe(data.head(), use_container_width=True)
            else:
                st.info("No data to export")

        with col2:
            st.write("**📤 Import Data**")
            uploaded_file = st.file_uploader("Choose CSV file", type="csv")

            if uploaded_file is not None:
                try:
                    import_df = pd.read_csv(uploaded_file)
                    st.write("**Preview of uploaded data:**")
                    st.dataframe(import_df.head())

                    # Validate columns
                    required_columns = ['date', 'product', 'price', 'location']
                    if all(col.lower() in [c.lower() for c in import_df.columns] for col in required_columns):
                        if st.button("📥 Import Data", type="primary"):
                            try:
                                # Normalize column names
                                import_df.columns = [col.lower() for col in import_df.columns]

                                # Convert date column
                                import_df['date'] = pd.to_datetime(import_df['date'])

                                # Insert data
                                with get_connection() as conn:
                                    cur = conn.cursor()
                                    for _, row in import_df.iterrows():
                                        cur.execute("""
                                            INSERT INTO market_data (date, product, price, location)
                                            VALUES (%s, %s, %s, %s)
                                        """, (row['date'].date(), row['product'], row['price'], row['location']))
                                    conn.commit()

                                st.success(f"✅ Successfully imported {len(import_df)} records!")
                                st.rerun()

                            except Exception as e:
                                st.error(f"❌ Error importing data: {e}")
                    else:
                        st.error(f"❌ CSV must contain columns: {', '.join(required_columns)}")

                except Exception as e:
                    st.error(f"❌ Error reading CSV file: {e}")

    with tab4:
        st.subheader("Data Statistics")

        data = load_market_data()
        if not data.empty:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("Total Records", len(data))
            with col2:
                st.metric("Products", len(data['Product'].unique()))
            with col3:
                st.metric("Locations", len(data['Location'].unique()))
            with col4:
                st.metric("Avg Price", f"{data['Price'].mean():,.0f} TZS")

            # Price statistics by product
            st.subheader("Price Statistics by Product")
            product_stats = data.groupby('Product')['Price'].agg(['count', 'mean', 'min', 'max']).round(2)
            product_stats.columns = ['Records', 'Avg Price', 'Min Price', 'Max Price']
            st.dataframe(product_stats, use_container_width=True)

            # Price statistics by location
            st.subheader("Price Statistics by Location")
            location_stats = data.groupby('Location')['Price'].agg(['count', 'mean', 'min', 'max']).round(2)
            location_stats.columns = ['Records', 'Avg Price', 'Min Price', 'Max Price']
            st.dataframe(location_stats, use_container_width=True)

        else:
            st.info("No data available for statistics")

def admin_email_testing():
    st.subheader("📧 Email & SMS System Testing")
    st.write("Test and configure email and SMS notification systems")

    # Create tabs for different testing functions
    tab1, tab2, tab3, tab4 = st.tabs(["🧪 Test Email", "📱 Test SMS", "⚙️ Configuration", "📊 Logs"])

    with tab1:
        st.subheader("Send Test Email")

        with st.form("test_email_form"):
            col1, col2 = st.columns(2)

            with col1:
                test_email = st.text_input("Recipient Email", placeholder="<EMAIL>")
                email_type = st.selectbox("Email Type", [
                    "Price Alert",
                    "Welcome Email",
                    "System Notification",
                    "Custom Message"
                ])

            with col2:
                if email_type == "Price Alert":
                    test_product = st.text_input("Product", value="Maize")
                    test_price = st.number_input("Current Price", value=1000, min_value=0)
                    test_threshold = st.number_input("Threshold", value=1200, min_value=0)
                elif email_type == "Custom Message":
                    custom_subject = st.text_input("Subject", value="Test Email from Smart Market")
                    custom_message = st.text_area("Message", value="This is a test email.")

            if st.form_submit_button("📤 Send Test Email", type="primary"):
                if test_email:
                    try:
                        if email_type == "Price Alert":
                            success = send_email_alert(
                                to_email=test_email,
                                product=test_product,
                                current_price=test_price,
                                threshold=test_threshold,
                                alert_type="below"
                            )
                        elif email_type == "Welcome Email":
                            success = send_welcome_email(test_email)
                        elif email_type == "System Notification":
                            success = send_system_notification(test_email, "System Test", "This is a test system notification.")
                        else:  # Custom Message
                            success = send_custom_email(test_email, custom_subject, custom_message)

                        if success:
                            st.success(f"✅ Test email sent successfully to {test_email}!")
                        else:
                            st.error("❌ Failed to send test email. Check configuration.")

                    except Exception as e:
                        st.error(f"❌ Error sending test email: {e}")
                else:
                    st.warning("⚠️ Please enter a recipient email address")

    with tab2:
        st.subheader("Send Test SMS")

        with st.form("test_sms_form"):
            col1, col2 = st.columns(2)

            with col1:
                test_phone = st.text_input("Recipient Phone Number", placeholder="+**********")
                sms_type = st.selectbox("SMS Type", [
                    "Price Alert",
                    "Welcome SMS",
                    "System Notification",
                    "Custom Message"
                ])

            with col2:
                if sms_type == "Price Alert":
                    test_product = st.text_input("Product", value="Maize")
                    test_price = st.number_input("Current Price", value=1000, min_value=0)
                    test_threshold = st.number_input("Threshold", value=1200, min_value=0)
                elif sms_type == "Welcome SMS":
                    test_username = st.text_input("Username", value="TestUser")
                elif sms_type == "Custom Message":
                    custom_subject = st.text_input("Subject", value="Test SMS from Smart Market")
                    custom_message = st.text_area("Message", value="This is a test SMS message.")

            if st.form_submit_button("📱 Send Test SMS", type="primary"):
                if test_phone:
                    try:
                        if sms_type == "Price Alert":
                            success, result = send_sms_alert(
                                to_phone=test_phone,
                                product=test_product,
                                current_price=test_price,
                                threshold=test_threshold,
                                alert_type="below"
                            )
                        elif sms_type == "Welcome SMS":
                            success, result = send_welcome_sms(test_phone, test_username)
                        elif sms_type == "System Notification":
                            success, result = send_system_sms(test_phone, "System Test", "This is a test system notification.")
                        else:  # Custom Message
                            success, result = send_system_sms(test_phone, custom_subject, custom_message)

                        if success:
                            st.success(f"✅ Test SMS sent successfully to {test_phone}!")
                            st.info(f"Message SID: {result}")
                        else:
                            st.error(f"❌ Failed to send test SMS: {result}")

                    except Exception as e:
                        st.error(f"❌ Error sending test SMS: {e}")
                else:
                    st.warning("⚠️ Please enter a recipient phone number")

    with tab3:
        st.subheader("Email & SMS Configuration")

        # Email Configuration
        st.write("**📧 Email Configuration**")
        try:
            smtp_server = st.secrets["email"]["smtp_server"]
            smtp_port = st.secrets["email"]["smtp_port"]
            email_user = st.secrets["email"]["email_user"]

            col1, col2 = st.columns(2)
            with col1:
                st.info(f"**SMTP Server:** {smtp_server}")
                st.info(f"**SMTP Port:** {smtp_port}")
            with col2:
                st.info(f"**Email User:** {email_user}")
                st.info(f"**Password:** {'*' * 10} (Hidden)")

            # Test email connection
            if st.button("🔗 Test SMTP Connection"):
                try:
                    import smtplib
                    with smtplib.SMTP(smtp_server, smtp_port) as server:
                        server.starttls()
                        server.login(email_user, st.secrets["email"]["email_password"])
                    st.success("✅ SMTP connection successful!")
                except Exception as e:
                    st.error(f"❌ SMTP connection failed: {e}")

        except KeyError as e:
            st.error(f"❌ Email configuration missing: {e}")
            st.info("Please configure email settings in .streamlit/secrets.toml")

        st.markdown("---")

        # SMS Configuration
        st.write("**📱 SMS Configuration (Twilio)**")
        try:
            account_sid = st.secrets["twilio"]["account_sid"]
            phone_number = st.secrets["twilio"]["phone_number"]

            col1, col2 = st.columns(2)
            with col1:
                st.info(f"**Account SID:** {account_sid[:8]}...{account_sid[-4:]} (Masked)")
                st.info(f"**Phone Number:** {phone_number}")
            with col2:
                st.info(f"**Auth Token:** {'*' * 10} (Hidden)")

                # Test SMS connection
                if st.button("📱 Test Twilio Connection"):
                    success, message = test_sms_connection()
                    if success:
                        st.success(f"✅ {message}")
                    else:
                        st.error(f"❌ {message}")

        except KeyError as e:
            st.error(f"❌ Twilio configuration missing: {e}")
            st.info("Please configure Twilio settings in .streamlit/secrets.toml")

            # Show example configuration
            st.code("""
[twilio]
account_sid = "your-account-sid"
auth_token = "your-auth-token"
phone_number = "+**********"
            """, language="toml")

    with tab4:
        st.subheader("Communication Activity Logs")
        st.info("📝 Email and SMS logging functionality would be implemented here in a production system.")

        # Create sub-tabs for email and SMS logs
        log_tab1, log_tab2 = st.tabs(["📧 Email Logs", "📱 SMS Logs"])

        with log_tab1:
            st.write("**Recent Email Activity**")
            # Simulate email logs
            email_logs = [
                {"timestamp": "2024-07-13 10:30:00", "recipient": "<EMAIL>", "type": "Price Alert", "status": "Sent"},
                {"timestamp": "2024-07-13 09:15:00", "recipient": "<EMAIL>", "type": "System Notification", "status": "Sent"},
                {"timestamp": "2024-07-13 08:45:00", "recipient": "<EMAIL>", "type": "Welcome Email", "status": "Failed"},
            ]

            for log in email_logs:
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.write(log["timestamp"])
                with col2:
                    st.write(log["recipient"])
                with col3:
                    st.write(log["type"])
                with col4:
                    if log["status"] == "Sent":
                        st.success(log["status"])
                    else:
                        st.error(log["status"])

        with log_tab2:
            st.write("**Recent SMS Activity**")
            # Simulate SMS logs
            sms_logs = [
                {"timestamp": "2024-07-13 11:00:00", "recipient": "+**********", "type": "Price Alert", "status": "Delivered", "sid": "SM1234..."},
                {"timestamp": "2024-07-13 10:45:00", "recipient": "+1987654321", "type": "Welcome SMS", "status": "Delivered", "sid": "SM5678..."},
                {"timestamp": "2024-07-13 10:30:00", "recipient": "+1555666777", "type": "System Alert", "status": "Failed", "sid": "SM9012..."},
            ]

            for log in sms_logs:
                col1, col2, col3, col4, col5 = st.columns(5)
                with col1:
                    st.write(log["timestamp"])
                with col2:
                    st.write(log["recipient"])
                with col3:
                    st.write(log["type"])
                with col4:
                    if log["status"] == "Delivered":
                        st.success(log["status"])
                    else:
                        st.error(log["status"])
                with col5:
                    st.caption(log["sid"])

# Additional email functions
def send_welcome_email(to_email):
    """Send welcome email to new users"""
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        smtp_server = st.secrets["email"]["smtp_server"]
        smtp_port = st.secrets["email"]["smtp_port"]
        sender_email = st.secrets["email"]["email_user"]
        sender_password = st.secrets["email"]["email_password"]

        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = to_email
        message["Subject"] = "🎉 Welcome to Smart Market!"

        body = f"""
        Welcome to Smart Market!

        Thank you for joining our platform. You now have access to:
        - Real-time market price tracking
        - AI-powered price predictions
        - Custom price alerts
        - Comprehensive market analytics

        Get started by exploring the Market Tracker and setting up your first price alert.

        Best regards,
        Smart Market Team
        """

        message.attach(MIMEText(body, "plain"))

        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)

        return True
    except Exception as e:
        print(f"Error sending welcome email: {e}")
        return False

def send_system_notification(to_email, subject, message_body):
    """Send system notification email"""
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        smtp_server = st.secrets["email"]["smtp_server"]
        smtp_port = st.secrets["email"]["smtp_port"]
        sender_email = st.secrets["email"]["email_user"]
        sender_password = st.secrets["email"]["email_password"]

        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = to_email
        message["Subject"] = f"🔔 Smart Market: {subject}"

        message.attach(MIMEText(message_body, "plain"))

        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)

        return True
    except Exception as e:
        print(f"Error sending system notification: {e}")
        return False

def send_custom_email(to_email, subject, message_body):
    """Send custom email"""
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        smtp_server = st.secrets["email"]["smtp_server"]
        smtp_port = st.secrets["email"]["smtp_port"]
        sender_email = st.secrets["email"]["email_user"]
        sender_password = st.secrets["email"]["email_password"]

        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = to_email
        message["Subject"] = subject

        message.attach(MIMEText(message_body, "plain"))

        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)

        return True
    except Exception as e:
        print(f"Error sending custom email: {e}")
        return False

# SMS Notification Functions using Twilio
def send_sms_alert(to_phone, product, current_price, threshold, alert_type="below"):
    """Send SMS alert when price threshold is reached"""
    try:
        # Twilio configuration from secrets.toml
        account_sid = st.secrets["twilio"]["account_sid"]
        auth_token = st.secrets["twilio"]["auth_token"]
        from_phone = st.secrets["twilio"]["phone_number"]

        # Create Twilio client
        client = Client(account_sid, auth_token)

        # Create SMS message
        message_body = f"""
🔔 Smart Market Price Alert

Product: {product}
Current Price: {current_price:,.0f} TZS
Your Threshold: {threshold:,.0f} TZS
Alert: Price went {alert_type} threshold

Time: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}

Smart Market Team
        """.strip()

        # Send SMS
        message = client.messages.create(
            body=message_body,
            from_=from_phone,
            to=to_phone
        )

        print(f"SMS sent successfully. SID: {message.sid}")
        return True, message.sid

    except Exception as e:
        print(f"Error sending SMS: {e}")
        st.error(f"❌ Failed to send SMS: {str(e)}")
        return False, str(e)

def send_welcome_sms(to_phone, username):
    """Send welcome SMS to new users"""
    try:
        account_sid = st.secrets["twilio"]["account_sid"]
        auth_token = st.secrets["twilio"]["auth_token"]
        from_phone = st.secrets["twilio"]["phone_number"]

        client = Client(account_sid, auth_token)

        message_body = f"""
🎉 Welcome to Smart Market!

Hi {username}! Your account is now active.

Features available:
• Real-time market prices
• Price alerts & notifications
• AI-powered predictions
• Market analytics

Start exploring now!

Smart Market Team
        """.strip()

        message = client.messages.create(
            body=message_body,
            from_=from_phone,
            to=to_phone
        )

        return True, message.sid

    except Exception as e:
        print(f"Error sending welcome SMS: {e}")
        return False, str(e)

def send_system_sms(to_phone, subject, message_text):
    """Send system notification SMS"""
    try:
        account_sid = st.secrets["twilio"]["account_sid"]
        auth_token = st.secrets["twilio"]["auth_token"]
        from_phone = st.secrets["twilio"]["phone_number"]

        client = Client(account_sid, auth_token)

        message_body = f"""
🔔 Smart Market: {subject}

{message_text}

Smart Market Team
        """.strip()

        message = client.messages.create(
            body=message_body,
            from_=from_phone,
            to=to_phone
        )

        return True, message.sid

    except Exception as e:
        print(f"Error sending system SMS: {e}")
        return False, str(e)

def test_sms_connection():
    """Test Twilio SMS connection"""
    try:
        account_sid = st.secrets["twilio"]["account_sid"]
        auth_token = st.secrets["twilio"]["auth_token"]

        client = Client(account_sid, auth_token)

        # Test by getting account info
        account = client.api.accounts(account_sid).fetch()

        return True, f"Connected to Twilio account: {account.friendly_name}"

    except Exception as e:
        return False, f"Twilio connection failed: {str(e)}"

def admin_security_testing():
    st.subheader("🔒 Security Testing & Validation")
    st.write("Test and validate security measures, input validation, and access controls")

    # Create tabs for different security tests
    tab1, tab2, tab3, tab4 = st.tabs(["🛡️ Access Control", "🔍 Input Validation", "💉 SQL Injection Tests", "📊 Security Report"])

    with tab1:
        st.subheader("Access Control Testing")

        # Test role-based access
        st.write("**Current User Session:**")
        col1, col2, col3 = st.columns(3)
        with col1:
            st.info(f"**Username:** {st.session_state.get('username', 'Not logged in')}")
        with col2:
            st.info(f"**Role:** {st.session_state.get('role', 'None')}")
        with col3:
            st.info(f"**Logged In:** {st.session_state.get('logged_in', False)}")

        # Test admin-only functions
        st.write("**Admin-Only Function Tests:**")
        if st.session_state.get('role') == 'admin':
            st.success("✅ Admin access confirmed - can access admin dashboard")
            st.success("✅ Admin access confirmed - can manage users")
            st.success("✅ Admin access confirmed - can view all feedback")
        else:
            st.error("❌ Non-admin user cannot access admin functions")

        # Test session management
        st.write("**Session Security:**")
        if 'logged_in' in st.session_state and st.session_state.logged_in:
            st.success("✅ Session state properly managed")
            if st.button("🔓 Test Logout"):
                logout()
        else:
            st.warning("⚠️ User not logged in - authentication required")

    with tab2:
        st.subheader("Input Validation Testing")

        # Test various input validations
        st.write("**Test Input Validation:**")

        with st.form("validation_test_form"):
            st.write("Try entering invalid data to test validation:")

            col1, col2 = st.columns(2)
            with col1:
                test_username = st.text_input("Username Test", placeholder="Try: admin'; DROP TABLE users; --")
                test_email = st.text_input("Email Test", placeholder="Try: invalid-email")
                test_price = st.number_input("Price Test", value=0, help="Try negative values")

            with col2:
                test_password = st.text_input("Password Test", type="password", placeholder="Try: 123")
                test_feedback = st.text_area("Feedback Test", placeholder="Try: <script>alert('XSS')</script>")

            if st.form_submit_button("🧪 Test Validation"):
                validation_results = []

                # Username validation
                if test_username:
                    if len(test_username.strip()) == 0:
                        validation_results.append("❌ Username: Empty input rejected")
                    elif "'" in test_username or '"' in test_username or ';' in test_username:
                        validation_results.append("⚠️ Username: Contains potentially dangerous characters")
                    else:
                        validation_results.append("✅ Username: Valid format")

                # Email validation
                if test_email:
                    import re
                    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                    if re.match(email_pattern, test_email):
                        validation_results.append("✅ Email: Valid format")
                    else:
                        validation_results.append("❌ Email: Invalid format")

                # Password validation
                if test_password:
                    if len(test_password) < 6:
                        validation_results.append("❌ Password: Too short (minimum 6 characters)")
                    else:
                        validation_results.append("✅ Password: Meets length requirement")

                # Price validation
                if test_price <= 0:
                    validation_results.append("❌ Price: Must be positive")
                else:
                    validation_results.append("✅ Price: Valid value")

                # XSS protection test
                if test_feedback:
                    if '<script>' in test_feedback.lower() or 'javascript:' in test_feedback.lower():
                        validation_results.append("⚠️ Feedback: Contains potential XSS content (would be sanitized)")
                    else:
                        validation_results.append("✅ Feedback: Safe content")

                for result in validation_results:
                    if result.startswith("✅"):
                        st.success(result)
                    elif result.startswith("❌"):
                        st.error(result)
                    else:
                        st.warning(result)

    with tab3:
        st.subheader("SQL Injection Prevention Testing")

        st.write("**Parameterized Query Testing:**")
        st.info("All database queries in this application use parameterized queries to prevent SQL injection.")

        # Show examples of safe queries
        st.write("**Examples of Safe Query Implementation:**")

        safe_queries = [
            {
                "Function": "User Login",
                "Query": "SELECT * FROM users WHERE username = %s",
                "Protection": "Parameterized query with %s placeholder"
            },
            {
                "Function": "Add User",
                "Query": "INSERT INTO users (username, password, email, role) VALUES (%s, %s, %s, %s)",
                "Protection": "All values parameterized"
            },
            {
                "Function": "Price Alert",
                "Query": "INSERT INTO price_alerts (username, product, location, target_price, alert_type, is_active) VALUES (%s, %s, %s, %s, %s, %s)",
                "Protection": "Complete parameterization"
            }
        ]

        for query in safe_queries:
            with st.expander(f"🔒 {query['Function']}"):
                st.code(query['Query'], language="sql")
                st.success(f"✅ {query['Protection']}")

        # Test SQL injection attempts
        st.write("**SQL Injection Attempt Simulation:**")
        if st.button("🧪 Simulate SQL Injection Test"):
            malicious_inputs = [
                "admin'; DROP TABLE users; --",
                "' OR '1'='1",
                "'; UPDATE users SET role='admin' WHERE username='user'; --"
            ]

            st.write("**Attempted malicious inputs:**")
            for input_val in malicious_inputs:
                st.code(input_val)

            st.success("✅ All queries use parameterized statements - SQL injection prevented!")
            st.info("💡 The psycopg2 library automatically escapes parameters when using %s placeholders.")

    with tab4:
        st.subheader("Security Report Summary")

        # Generate security report
        security_checks = [
            {"Check": "Password Hashing", "Status": "✅ PASS", "Details": "bcrypt with salt used for all passwords"},
            {"Check": "SQL Injection Prevention", "Status": "✅ PASS", "Details": "Parameterized queries used throughout"},
            {"Check": "Session Management", "Status": "✅ PASS", "Details": "Streamlit session state properly managed"},
            {"Check": "Role-Based Access Control", "Status": "✅ PASS", "Details": "Admin functions restricted to admin role"},
            {"Check": "Input Validation", "Status": "✅ PASS", "Details": "Form validation and data sanitization"},
            {"Check": "Database Connection Security", "Status": "✅ PASS", "Details": "Credentials stored in secrets.toml"},
            {"Check": "Email Configuration Security", "Status": "✅ PASS", "Details": "SMTP credentials in secrets.toml"},
            {"Check": "Error Handling", "Status": "✅ PASS", "Details": "Try-catch blocks prevent information disclosure"},
        ]

        st.write("**Security Assessment Results:**")

        for check in security_checks:
            col1, col2, col3 = st.columns([2, 1, 3])
            with col1:
                st.write(f"**{check['Check']}**")
            with col2:
                if check['Status'].startswith("✅"):
                    st.success(check['Status'])
                else:
                    st.error(check['Status'])
            with col3:
                st.write(check['Details'])

        # Overall security score
        passed_checks = sum(1 for check in security_checks if check['Status'].startswith("✅"))
        total_checks = len(security_checks)
        security_score = (passed_checks / total_checks) * 100

        st.markdown("---")
        st.write("**Overall Security Score:**")

        if security_score >= 90:
            st.success(f"🛡️ Excellent: {security_score:.0f}% ({passed_checks}/{total_checks} checks passed)")
        elif security_score >= 70:
            st.warning(f"⚠️ Good: {security_score:.0f}% ({passed_checks}/{total_checks} checks passed)")
        else:
            st.error(f"❌ Needs Improvement: {security_score:.0f}% ({passed_checks}/{total_checks} checks passed)")

        # Recommendations
        st.write("**Security Recommendations:**")
        recommendations = [
            "🔒 Implement rate limiting for login attempts",
            "📝 Add comprehensive audit logging",
            "🔐 Consider implementing 2FA for admin accounts",
            "🛡️ Regular security updates and dependency scanning",
            "📊 Implement CSRF protection for forms",
            "🔍 Add input length limits and content filtering"
        ]

        for rec in recommendations:
            st.info(rec)

# Change Password
def change_password():
    set_custom_style()

    # Modern page header
    st.markdown("""
        <div style='text-align: center; margin-bottom: 2rem; padding: 2rem;
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    border-radius: 20px; color: white; box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);'>
            <h1 style='margin: 0; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;'>
                🔐 Change Password
            </h1>
            <p style='margin: 0; font-size: 1.1rem; opacity: 0.9;'>
                Update your account password, {st.session_state.username}
            </p>
        </div>
    """, unsafe_allow_html=True)

    # Use a form to enable clearing after successful submission
    with st.form("change_password_form", clear_on_submit=True):
        st.write("**Enter your password details:**")

        current_pw = st.text_input("Current Password", type="password", help="Enter your current password")
        new_pw = st.text_input("New Password", type="password", help="Enter your new password")
        confirm_pw = st.text_input("Confirm New Password", type="password", help="Confirm your new password")

        submitted = st.form_submit_button("Update Password", type="primary")

        if submitted:
            if not current_pw or not new_pw or not confirm_pw:
                st.warning("❌ All fields are required.")
            elif len(new_pw) < 6:
                st.warning("❌ New password must be at least 6 characters long.")
            elif new_pw != confirm_pw:
                st.error("❌ New passwords do not match.")
            else:
                try:
                    with get_connection() as conn:
                        cur = conn.cursor()
                        cur.execute("SELECT password FROM users WHERE username = %s", (st.session_state.username,))
                        result = cur.fetchone()

                        if not result or not bcrypt.checkpw(current_pw.encode(), result[0].encode()):
                            st.error("❌ Current password is incorrect.")
                        else:
                            # Hash the new password
                            hashed_new_pw = bcrypt.hashpw(new_pw.encode(), bcrypt.gensalt()).decode()

                            # Update the password
                            cur.execute("UPDATE users SET password = %s WHERE username = %s", (hashed_new_pw, st.session_state.username))
                            conn.commit()

                            st.success("🎉 Password updated successfully!")

                            # Redirect based on user role
                            if st.session_state.role == 'admin':
                                st.info("Redirecting to Admin Dashboard...")
                                st.session_state.current_page = "AdminDashboard"
                            else:
                                st.info("Redirecting to Market Tracker...")
                                st.session_state.current_page = "MarketTracker"

                            # Force a rerun to redirect
                            time.sleep(1)
                            st.rerun()

                except Exception as e:
                    st.error(f"❌ Failed to update password: {e}")
                finally:
                    if 'cur' in locals():
                        cur.close()

# Feedback System
def feedback_system():
    st.header("💬 Feedback System")
    st.write("Share your thoughts and help us improve the Smart Market platform")

    # Create tabs for different feedback functions
    if st.session_state['role'] == 'admin':
        tab1, tab2 = st.tabs(["📋 All Feedback", "📊 Feedback Analytics"])
    else:
        tab1, tab2, tab3 = st.tabs(["✍️ Submit Feedback", "📋 My Feedback", "📊 Community Feedback"])

    if st.session_state['role'] != 'admin':
        with tab1:
            st.subheader("Submit Your Feedback")

            with st.form("feedback_form", clear_on_submit=True):
                feedback_type = st.selectbox(
                    "Feedback Type",
                    ["General", "Bug Report", "Feature Request", "Improvement Suggestion", "Complaint", "Compliment"]
                )

                feedback_text = st.text_area(
                    "Your Feedback",
                    placeholder="Please share your thoughts, suggestions, or report any issues...",
                    height=150
                )

                rating = st.slider("Overall Rating", 1, 5, 3, help="Rate your experience from 1 (poor) to 5 (excellent)")

                submitted = st.form_submit_button("📤 Submit Feedback", type="primary")

                if submitted:
                    if feedback_text.strip():
                        try:
                            with get_connection() as conn:
                                cur = conn.cursor()
                                cur.execute("""
                                    INSERT INTO feedback (username, feedback, feedback_type, rating)
                                    VALUES (%s, %s, %s, %s)
                                """, (st.session_state['username'], feedback_text.strip(), feedback_type, rating))
                                conn.commit()
                            st.success("✅ Thank you for your feedback! We appreciate your input.")
                            st.rerun()
                        except Exception as e:
                            # Handle case where feedback_type and rating columns don't exist
                            try:
                                with get_connection() as conn:
                                    cur = conn.cursor()
                                    cur.execute("""
                                        INSERT INTO feedback (username, feedback)
                                        VALUES (%s, %s)
                                    """, (st.session_state['username'], feedback_text.strip()))
                                    conn.commit()
                                st.success("✅ Thank you for your feedback! We appreciate your input.")
                                st.rerun()
                            except Exception as e2:
                                st.error(f"❌ Error submitting feedback: {e2}")
                    else:
                        st.warning("⚠️ Please enter some feedback before submitting.")

        with tab2:
            st.subheader("My Feedback History")

            try:
                with get_connection() as conn:
                    cur = conn.cursor()
                    cur.execute("""
                        SELECT id, feedback, timestamp, feedback_type, rating
                        FROM feedback
                        WHERE username = %s
                        ORDER BY timestamp DESC
                    """, (st.session_state['username'],))
                    user_feedback = cur.fetchall()
            except:
                # Fallback for tables without new columns
                with get_connection() as conn:
                    cur = conn.cursor()
                    cur.execute("""
                        SELECT id, feedback, timestamp
                        FROM feedback
                        WHERE username = %s
                        ORDER BY timestamp DESC
                    """, (st.session_state['username'],))
                    user_feedback = cur.fetchall()

            if user_feedback:
                for feedback in user_feedback:
                    with st.container():
                        col1, col2 = st.columns([5, 1])

                        with col1:
                            if len(feedback) >= 5:  # New format with type and rating
                                feedback_id, text, timestamp, f_type, rating = feedback
                                st.markdown(f"**{f_type}** - {timestamp.strftime('%Y-%m-%d %H:%M')}")
                                if rating:
                                    st.markdown(f"Rating: {'⭐' * rating}")
                            else:  # Old format
                                feedback_id, text, timestamp = feedback
                                st.markdown(f"**Feedback** - {timestamp.strftime('%Y-%m-%d %H:%M')}")

                            st.write(f"💬 {text}")

                        with col2:
                            if st.button("🗑️", key=f"delete_my_feedback_{feedback_id}", help="Delete feedback"):
                                st.session_state[f"confirm_delete_my_feedback_{feedback_id}"] = True
                                st.rerun()

                        # Delete confirmation
                        if st.session_state.get(f"confirm_delete_my_feedback_{feedback_id}", False):
                            st.warning("⚠️ Are you sure you want to delete this feedback?")
                            col_confirm, col_cancel = st.columns(2)
                            with col_confirm:
                                if st.button("✅ Confirm", key=f"confirm_del_my_feedback_{feedback_id}", type="primary"):
                                    try:
                                        with get_connection() as conn:
                                            cur = conn.cursor()
                                            cur.execute("DELETE FROM feedback WHERE id = %s AND username = %s",
                                                      (feedback_id, st.session_state['username']))
                                            conn.commit()
                                        st.success("✅ Feedback deleted successfully!")
                                        st.session_state[f"confirm_delete_my_feedback_{feedback_id}"] = False
                                        st.rerun()
                                    except Exception as e:
                                        st.error(f"❌ Error deleting feedback: {e}")
                            with col_cancel:
                                if st.button("❌ Cancel", key=f"cancel_del_my_feedback_{feedback_id}"):
                                    st.session_state[f"confirm_delete_my_feedback_{feedback_id}"] = False
                                    st.rerun()

                        st.divider()
            else:
                st.info("📭 You haven't submitted any feedback yet. Use the 'Submit Feedback' tab to share your thoughts!")

        with tab3:
            st.subheader("Community Feedback")
            st.write("See what other users are saying (anonymized)")

            try:
                with get_connection() as conn:
                    cur = conn.cursor()
                    cur.execute("""
                        SELECT feedback, timestamp, feedback_type, rating
                        FROM feedback
                        ORDER BY timestamp DESC
                        LIMIT 20
                    """)
                    community_feedback = cur.fetchall()
            except:
                # Fallback for tables without new columns
                with get_connection() as conn:
                    cur = conn.cursor()
                    cur.execute("""
                        SELECT feedback, timestamp
                        FROM feedback
                        ORDER BY timestamp DESC
                        LIMIT 20
                    """)
                    community_feedback = cur.fetchall()

            if community_feedback:
                for feedback in community_feedback:
                    with st.container():
                        if len(feedback) >= 4:  # New format
                            text, timestamp, f_type, rating = feedback
                            st.markdown(f"**{f_type}** - {timestamp.strftime('%Y-%m-%d')}")
                            if rating:
                                st.markdown(f"Rating: {'⭐' * rating}")
                        else:  # Old format
                            text, timestamp = feedback
                            st.markdown(f"**Feedback** - {timestamp.strftime('%Y-%m-%d')}")

                        st.write(f"💬 {text}")
                        st.divider()
            else:
                st.info("📭 No community feedback available yet.")

    else:  # Admin view
        with tab1:
            admin_feedback_management()

        with tab2:
            st.subheader("Feedback Analytics")

            try:
                with get_connection() as conn:
                    cur = conn.cursor()

                    # Get feedback statistics
                    cur.execute("SELECT COUNT(*) FROM feedback")
                    total_feedback = cur.fetchone()[0]

                    cur.execute("SELECT COUNT(DISTINCT username) FROM feedback")
                    unique_users = cur.fetchone()[0]

                    # Try to get rating statistics
                    try:
                        cur.execute("SELECT AVG(rating) FROM feedback WHERE rating IS NOT NULL")
                        avg_rating = cur.fetchone()[0]
                        avg_rating = round(avg_rating, 1) if avg_rating else "N/A"
                    except:
                        avg_rating = "N/A"

                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Total Feedback", total_feedback)
                    with col2:
                        st.metric("Active Users", unique_users)
                    with col3:
                        st.metric("Average Rating", avg_rating)

                    # Feedback trends
                    cur.execute("""
                        SELECT DATE(timestamp) as date, COUNT(*) as count
                        FROM feedback
                        WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days'
                        GROUP BY DATE(timestamp)
                        ORDER BY date
                    """)
                    trend_data = cur.fetchall()

                    if trend_data:
                        import matplotlib.pyplot as plt
                        dates = [row[0] for row in trend_data]
                        counts = [row[1] for row in trend_data]

                        fig, ax = plt.subplots(figsize=(10, 4))
                        ax.plot(dates, counts, marker='o')
                        ax.set_title("Feedback Submissions (Last 30 Days)")
                        ax.set_xlabel("Date")
                        ax.set_ylabel("Number of Feedback")
                        plt.xticks(rotation=45)
                        plt.tight_layout()
                        st.pyplot(fig)
                        plt.close()

            except Exception as e:
                st.error(f"Error loading analytics: {e}")


# --- Sample Data for Market Analysis in Tanzania ---
def get_sample_market_data():
    # Sample data for Tanzanian market analysis (Date, Product, Price, Location)
    data = [
        ["2024-01-01", "Maize", 1200, "Dar es Salaam"],
        ["2024-01-08", "Maize", 1250, "Dar es Salaam"],
        ["2024-01-15", "Maize", 1300, "Dar es Salaam"],
        ["2024-01-22", "Maize", 1280, "Dar es Salaam"],
        ["2024-01-29", "Maize", 1320, "Dar es Salaam"],
        ["2024-02-05", "Maize", 1350, "Dar es Salaam"],
        ["2024-01-01", "Rice", 2000, "Mwanza"],
        ["2024-01-08", "Rice", 2020, "Mwanza"],
        ["2024-01-15", "Rice", 2050, "Mwanza"],
        ["2024-01-22", "Rice", 2100, "Mwanza"],
        ["2024-01-29", "Rice", 2080, "Mwanza"],
        ["2024-02-05", "Rice", 2120, "Mwanza"],
        ["2024-01-01", "Beans", 1800, "Arusha"],
        ["2024-01-08", "Beans", 1820, "Arusha"],
        ["2024-01-15", "Beans", 1850, "Arusha"],
        ["2024-01-22", "Beans", 1870, "Arusha"],
        ["2024-01-29", "Beans", 1900, "Arusha"],
        ["2024-02-05", "Beans", 1920, "Arusha"],
    ]
    df = pd.DataFrame(data, columns=["Date", "Product", "Price", "Location"])
    df["Date"] = pd.to_datetime(df["Date"])
    return df

# --- Enhanced ML Model for Price Prediction ---
def train_price_predictor(df, product, location):
    """
    Enhanced price prediction model with multiple features and better accuracy
    """
    # Filter for product and location
    df = df[(df["Product"] == product) & (df["Location"] == location)].copy()
    df = df.sort_values("Date")

    if len(df) < 3:
        return None, None, None

    # Create enhanced features
    df["Days"] = (df["Date"] - df["Date"].min()).dt.days
    df["DayOfWeek"] = df["Date"].dt.dayofweek  # 0=Monday, 6=Sunday
    df["Month"] = df["Date"].dt.month
    df["Quarter"] = df["Date"].dt.quarter
    df["DayOfYear"] = df["Date"].dt.dayofyear

    # Add trend features
    df["Price_MA3"] = df["Price"].rolling(window=3, min_periods=1).mean()  # 3-period moving average
    df["Price_Lag1"] = df["Price"].shift(1).fillna(df["Price"].iloc[0])  # Previous price
    df["Price_Trend"] = df["Price"].diff().fillna(0)  # Price change from previous period

    # Add seasonal features
    df["Sin_DayOfYear"] = np.sin(2 * np.pi * df["DayOfYear"] / 365.25)
    df["Cos_DayOfYear"] = np.cos(2 * np.pi * df["DayOfYear"] / 365.25)
    df["Sin_Month"] = np.sin(2 * np.pi * df["Month"] / 12)
    df["Cos_Month"] = np.cos(2 * np.pi * df["Month"] / 12)

    # Prepare features
    feature_columns = [
        "Days", "DayOfWeek", "Month", "Quarter", "DayOfYear",
        "Price_MA3", "Price_Lag1", "Price_Trend",
        "Sin_DayOfYear", "Cos_DayOfYear", "Sin_Month", "Cos_Month"
    ]

    X = df[feature_columns].values
    y = df["Price"].values

    # Use an ensemble of models for better prediction

    # Create ensemble model
    models = {
        'rf': RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10),
        'gb': GradientBoostingRegressor(n_estimators=100, random_state=42, max_depth=6),
        'lr': LinearRegression()
    }

    # Scale features for linear regression
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # Train models
    trained_models = {}
    for name, model in models.items():
        if name == 'lr':
            model.fit(X_scaled, y)
        else:
            model.fit(X, y)
        trained_models[name] = model

    # Create ensemble predictor
    class EnsemblePredictor:
        def __init__(self, models, scaler, feature_columns, base_date, train_df):
            self.models = models
            self.scaler = scaler
            self.feature_columns = feature_columns
            self.base_date = base_date
            self.train_df = train_df
            self.last_price = train_df["Price"].iloc[-1]
            self.last_ma3 = train_df["Price_MA3"].iloc[-1]
            self.last_trend = train_df["Price_Trend"].iloc[-1]

        def predict(self, days_since_base):
            """Predict prices for given days since base date"""
            predictions = []

            for days in days_since_base.flatten():
                # Calculate date features
                pred_date = self.base_date + pd.Timedelta(days=int(days))

                # Create feature vector
                features = {
                    "Days": days,
                    "DayOfWeek": pred_date.dayofweek,
                    "Month": pred_date.month,
                    "Quarter": pred_date.quarter,
                    "DayOfYear": pred_date.dayofyear,
                    "Price_MA3": self.last_ma3,  # Use last known MA
                    "Price_Lag1": self.last_price,  # Use last known price
                    "Price_Trend": self.last_trend,  # Use last known trend
                    "Sin_DayOfYear": np.sin(2 * np.pi * pred_date.dayofyear / 365.25),
                    "Cos_DayOfYear": np.cos(2 * np.pi * pred_date.dayofyear / 365.25),
                    "Sin_Month": np.sin(2 * np.pi * pred_date.month / 12),
                    "Cos_Month": np.cos(2 * np.pi * pred_date.month / 12)
                }

                X_pred = np.array([features[col] for col in self.feature_columns]).reshape(1, -1)

                # Get predictions from all models
                rf_pred = self.models['rf'].predict(X_pred)[0]
                gb_pred = self.models['gb'].predict(X_pred)[0]
                lr_pred = self.models['lr'].predict(self.scaler.transform(X_pred))[0]

                # Ensemble prediction (weighted average)
                ensemble_pred = 0.4 * rf_pred + 0.4 * gb_pred + 0.2 * lr_pred

                # Update last values for next prediction
                self.last_price = ensemble_pred
                self.last_ma3 = (self.last_ma3 * 2 + ensemble_pred) / 3  # Simple MA update

                predictions.append(ensemble_pred)

            return np.array(predictions)

    # Create ensemble predictor
    ensemble_model = EnsemblePredictor(trained_models, scaler, feature_columns, df["Date"].min(), df)

    return ensemble_model, df["Date"].min(), df

# --- Market Price Tracker Section ---
def market_price_tracking():
    set_custom_style()

    # Modern page header
    st.markdown("""
        <div style='text-align: center; margin-bottom: 2rem; padding: 2rem;
                    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
                    border-radius: 20px; color: white; box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);'>
            <h1 style='margin: 0; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;'>
                📊 Market Price Tracker
            </h1>
            <p style='margin: 0; font-size: 1.1rem; opacity: 0.9;'>
                Real-time market analysis with AI-powered predictions
            </p>
        </div>
    """, unsafe_allow_html=True)

    try:
        # The model is trained on this sample data:
        data = get_sample_market_data()
        # Modern Dashboard Grid
        st.markdown('<div class="dashboard-grid">', unsafe_allow_html=True)
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.markdown(f"""
                <div class="info-box">
                    <span class="info-box-icon" style="color:#17a2b8;">📦</span>
                    <div class="info-box-text">Total Products</div>
                    <div class="info-box-number">{len(data['Product'].unique())}</div>
                </div>
            """, unsafe_allow_html=True)
        with col2:
            st.markdown(f"""
                <div class="info-box">
                    <span class="info-box-icon" style="color:#28a745;">📍</span>
                    <div class="info-box-text">Locations</div>
                    <div class="info-box-number">{len(data['Location'].unique())}</div>
                </div>
            """, unsafe_allow_html=True)
        with col3:
            st.markdown(f"""
                <div class="info-box">
                    <span class="info-box-icon" style="color:#ffc107;">💲</span>
                    <div class="info-box-text">Average Price</div>
                    <div class="info-box-number">${data['Price'].mean():.2f}</div>
                </div>
            """, unsafe_allow_html=True)
        with col4:
            st.markdown(f"""
                <div class="info-box">
                    <span class="info-box-icon" style="color:#dc3545;">⏰</span>
                    <div class="info-box-text">Latest Update</div>
                    <div class="info-box-number">{data['Date'].max().strftime('%Y-%m-%d')}</div>
                </div>
            """, unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

        # Filters Card
        with st.container():
            st.markdown("""
                <div class="card">
                    <div class="card-header">
                        <span class="card-title">Filter Data</span>
                    </div>
                    <div class="card-body">
            """, unsafe_allow_html=True)
            col1, col2, col3 = st.columns(3)
            with col1:
                product_filter = st.selectbox("Select Product", data['Product'].unique())
            with col2:
                location_filter = st.selectbox("Select Location", data['Location'].unique())
            with col3:
                min_date = data['Date'].min().date()
                max_date = data['Date'].max().date()
                today = data['Date'].max().date()
                # Allow picking dates up to 3 years in the future
                future_max = today.replace(year=today.year + 3)
                # Use two date_input widgets for start and end date (date picker)
                start_date = st.date_input(
                    "Start Date",
                    value=min_date,
                    min_value=min_date,
                    max_value=future_max
                )
                end_date = st.date_input(
                    "End Date",
                    value=today,
                    min_value=min_date,
                    max_value=future_max
                )
                if start_date > end_date:
                    st.warning("Start date cannot be after end date.")
            st.markdown("</div></div>", unsafe_allow_html=True)

        # Alert threshold input
        st.markdown("#### Set Price Alert")
        alert_email = st.text_input("Your Email for Alerts", value=st.session_state.get("username", ""))
        alert_threshold = st.number_input(
            f"Alert me when {product_filter} price in {location_filter} drops below (TZS):",
            min_value=0, value=1000, step=10, key="alert_threshold"
        )

        # Filtered data (only for actual data, not future)
        filtered_data = data[
            (data['Product'] == product_filter) &
            (data['Location'] == location_filter) &
            (data['Date'] >= pd.to_datetime(start_date)) &
            (data['Date'] <= pd.to_datetime(min(end_date, data['Date'].max().date())))
        ].copy()

        # Enhanced alert system for actual data
        if not filtered_data.empty and alert_email and alert_threshold > 0:
            below_actual = filtered_data[filtered_data["Price"] < alert_threshold]
            if not below_actual.empty:
                if st.button(f"Send Alert for Current Price Below {alert_threshold} TZS"):
                    sent = send_email_alert(
                        to_email=alert_email,
                        product=product_filter,
                        current_price=int(below_actual.iloc[0]["Price"]),
                        threshold=alert_threshold
                    )
                    if sent:
                        st.success(f"Alert sent to {alert_email} for {product_filter} below {alert_threshold} TZS (actual data).")
                    else:
                        st.error("Failed to send alert.")
            
            # Add automatic alert option
            auto_alert = st.checkbox("Enable automatic alerts when price reaches threshold", value=False)
            if auto_alert:
                st.info(f"Automatic alerts enabled. You will receive an email when {product_filter} price in {location_filter} drops below {alert_threshold} TZS.")
                # This would typically connect to a background process or scheduled task
                # For demo purposes, we'll simulate this with a button
                if st.button("Test automatic alert"):
                    sent = send_email_alert(
                        to_email=alert_email,
                        product=product_filter,
                        current_price=int(filtered_data["Price"].min()),
                        threshold=alert_threshold
                    )
                    if sent:
                        st.success(f"Test alert sent to {alert_email}.")
                    else:
                        st.error("Failed to send test alert.")

        # --- Enhanced ML Prediction Section ---
        st.subheader("🔮 Advanced Market Price Prediction")

        # Prediction time frame selection
        col1, col2 = st.columns(2)
        with col1:
            prediction_period = st.selectbox(
                "Select Prediction Period:",
                ["Next 2 Weeks", "Next Month", "Next 3 Months", "Next 6 Months", "Custom Range"],
                help="Choose how far into the future you want to predict prices"
            )

        with col2:
            prediction_frequency = st.selectbox(
                "Prediction Frequency:",
                ["Daily", "Weekly", "Bi-weekly", "Monthly"],
                index=1,  # Default to Weekly
                help="How often to show predictions within the selected period"
            )

        # Custom date range if selected
        if prediction_period == "Custom Range":
            col1, col2 = st.columns(2)
            with col1:
                custom_start = st.date_input(
                    "Prediction Start Date",
                    value=pd.Timestamp.now().date() + pd.Timedelta(days=1),
                    min_value=pd.Timestamp.now().date()
                )
            with col2:
                custom_end = st.date_input(
                    "Prediction End Date",
                    value=pd.Timestamp.now().date() + pd.Timedelta(days=30),
                    min_value=custom_start if 'custom_start' in locals() else pd.Timestamp.now().date()
                )

        model, base_date, train_df = train_price_predictor(data, product_filter, location_filter)
        if model is not None:
            # Calculate prediction dates based on selection
            today = pd.Timestamp.now().date()

            if prediction_period == "Next 2 Weeks":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=14)
            elif prediction_period == "Next Month":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=30)
            elif prediction_period == "Next 3 Months":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=90)
            elif prediction_period == "Next 6 Months":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=180)
            else:  # Custom Range
                pred_start = custom_start
                pred_end = custom_end

            # Set frequency
            freq_map = {
                "Daily": "D",
                "Weekly": "7D",
                "Bi-weekly": "14D",
                "Monthly": "30D"
            }
            freq = freq_map[prediction_frequency]

            # Generate prediction dates
            future_dates = pd.date_range(start=pred_start, end=pred_end, freq=freq)

            if len(future_dates) > 0:
                # Make predictions
                days_since_base = np.array([(pd.Timestamp(d) - pd.Timestamp(base_date)).days for d in future_dates]).reshape(-1, 1)
                preds = model.predict(days_since_base)

                # Create prediction dataframe
                pred_df = pd.DataFrame({
                    "Date": future_dates,
                    "Predicted Price (TZS)": preds.astype(int),
                    "Day of Week": [d.strftime('%A') for d in future_dates],
                    "Week of Year": [d.isocalendar()[1] for d in future_dates]
                })

                # Add price change indicators
                pred_df["Price Change"] = pred_df["Predicted Price (TZS)"].diff()
                pred_df["Trend"] = pred_df["Price Change"].apply(
                    lambda x: "📈 Rising" if x > 0 else "📉 Falling" if x < 0 else "➡️ Stable"
                )

                # Display prediction summary
                st.markdown("### 📊 Prediction Summary")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    avg_price = pred_df["Predicted Price (TZS)"].mean()
                    st.metric("Average Price", f"{avg_price:.0f} TZS")

                with col2:
                    min_price = pred_df["Predicted Price (TZS)"].min()
                    min_date = pred_df.loc[pred_df["Predicted Price (TZS)"].idxmin(), "Date"].strftime('%Y-%m-%d')
                    st.metric("Lowest Price", f"{min_price:.0f} TZS", f"on {min_date}")

                with col3:
                    max_price = pred_df["Predicted Price (TZS)"].max()
                    max_date = pred_df.loc[pred_df["Predicted Price (TZS)"].idxmax(), "Date"].strftime('%Y-%m-%d')
                    st.metric("Highest Price", f"{max_price:.0f} TZS", f"on {max_date}")

                with col4:
                    price_volatility = pred_df["Predicted Price (TZS)"].std()
                    st.metric("Price Volatility", f"{price_volatility:.0f} TZS")

                # Show detailed predictions table
                st.markdown("### 📅 Detailed Price Predictions")

                # Format the dataframe for better display
                display_df = pred_df.copy()
                display_df["Date"] = display_df["Date"].dt.strftime('%Y-%m-%d')
                display_df["Price Change"] = display_df["Price Change"].fillna(0).astype(int)

                st.dataframe(
                    display_df[["Date", "Day of Week", "Predicted Price (TZS)", "Price Change", "Trend"]],
                    use_container_width=True
                )

                # Enhanced visualization
                st.markdown("### 📈 Price Prediction Chart")

                fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

                # Main prediction chart
                ax1.plot(train_df["Date"], train_df["Price"], marker="o", linewidth=2, label="Historical Prices", color="blue")
                ax1.plot(pred_df["Date"], pred_df["Predicted Price (TZS)"], marker="x", linestyle="--", linewidth=2, label="Predicted Prices", color="orange")
                ax1.axvline(x=pd.Timestamp(today), color='red', linestyle=':', alpha=0.7, label='Today')
                ax1.fill_between(pred_df["Date"], pred_df["Predicted Price (TZS)"] * 0.95, pred_df["Predicted Price (TZS)"] * 1.05, alpha=0.2, color="orange", label="Confidence Band (±5%)")
                ax1.set_xlabel("Date")
                ax1.set_ylabel("Price (TZS)")
                ax1.set_title(f"{product_filter} Price Forecast - {location_filter} ({prediction_period})")
                ax1.legend()
                ax1.grid(True, alpha=0.3)

                # Price change chart
                colors = ['green' if x < 0 else 'red' if x > 0 else 'gray' for x in pred_df["Price Change"].fillna(0)]
                ax2.bar(pred_df["Date"], pred_df["Price Change"].fillna(0), color=colors, alpha=0.7)
                ax2.set_xlabel("Date")
                ax2.set_ylabel("Price Change (TZS)")
                ax2.set_title("Predicted Price Changes")
                ax2.grid(True, alpha=0.3)
                ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

                plt.tight_layout()
                st.pyplot(fig)
                plt.close()

                # Market insights and recommendations
                st.markdown("### 💡 Market Insights & Recommendations")

                # Calculate trends
                first_price = pred_df["Predicted Price (TZS)"].iloc[0]
                last_price = pred_df["Predicted Price (TZS)"].iloc[-1]
                overall_change = ((last_price - first_price) / first_price) * 100

                # Best times to buy/sell
                best_buy_idx = pred_df["Predicted Price (TZS)"].idxmin()
                best_sell_idx = pred_df["Predicted Price (TZS)"].idxmax()

                best_buy_date = pred_df.loc[best_buy_idx, "Date"].strftime('%Y-%m-%d')
                best_buy_price = pred_df.loc[best_buy_idx, "Predicted Price (TZS)"]

                best_sell_date = pred_df.loc[best_sell_idx, "Date"].strftime('%Y-%m-%d')
                best_sell_price = pred_df.loc[best_sell_idx, "Predicted Price (TZS)"]

                col1, col2 = st.columns(2)

                with col1:
                    trend_color = "green" if overall_change < 0 else "red" if overall_change > 0 else "gray"
                    trend_icon = "📉" if overall_change < 0 else "📈" if overall_change > 0 else "➡️"

                    st.markdown(f"""
                    <div style='background-color: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid {trend_color};'>
                        <h4>{trend_icon} Overall Trend</h4>
                        <p>Price is expected to <strong style='color: {trend_color};'>
                        {"decrease" if overall_change < 0 else "increase" if overall_change > 0 else "remain stable"}
                        </strong> by <strong>{abs(overall_change):.1f}%</strong> over the {prediction_period.lower()}.</p>
                        <p><strong>Best time to buy:</strong> {best_buy_date} at {best_buy_price} TZS</p>
                        <p><strong>Best time to sell:</strong> {best_sell_date} at {best_sell_price} TZS</p>
                    </div>
                    """, unsafe_allow_html=True)

                with col2:
                    # Trading recommendations
                    if overall_change < -5:
                        recommendation = "🟢 STRONG BUY - Prices expected to fall significantly"
                        rec_color = "green"
                    elif overall_change < -2:
                        recommendation = "🟡 BUY - Moderate price decrease expected"
                        rec_color = "orange"
                    elif overall_change > 5:
                        recommendation = "🔴 SELL - Prices expected to rise significantly"
                        rec_color = "red"
                    elif overall_change > 2:
                        recommendation = "🟡 HOLD/SELL - Moderate price increase expected"
                        rec_color = "orange"
                    else:
                        recommendation = "⚪ HOLD - Prices expected to remain stable"
                        rec_color = "gray"

                    st.markdown(f"""
                    <div style='background-color: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid {rec_color};'>
                        <h4>📋 Trading Recommendation</h4>
                        <p><strong>{recommendation}</strong></p>
                        <p>Volatility: <strong>{"High" if price_volatility > 100 else "Medium" if price_volatility > 50 else "Low"}</strong></p>
                        <p>Confidence: <strong>{"High" if len(train_df) > 10 else "Medium" if len(train_df) > 5 else "Low"}</strong></p>
                    </div>
                    """, unsafe_allow_html=True)

                # Alert logic with enhanced predictions
                if alert_email and alert_threshold > 0:
                    below_threshold = pred_df[pred_df["Predicted Price (TZS)"] < alert_threshold]
                    if not below_threshold.empty:
                        st.warning(f"⚠️ Price is predicted to drop below your threshold of {alert_threshold} TZS on {len(below_threshold)} occasions!")
                        if st.button("📧 Send Detailed Price Alert"):
                            # Send enhanced alert with prediction details
                            sent = send_email_alert(
                                to_email=alert_email,
                                product=product_filter,
                                current_price=int(below_threshold.iloc[0]["Predicted Price (TZS)"]),
                                threshold=alert_threshold
                            )
                            if sent:
                                st.success(f"📧 Detailed price alert sent to {alert_email}!")
                            else:
                                st.error("❌ Failed to send alert.")

            else:
                st.info("No prediction dates available for the selected range. Please adjust your selection.")
        else:
            st.info("⚠️ Not enough historical data for ML prediction. Please select a product/location with more data points.")

        if filtered_data.empty:
            st.warning("No data available for the selected filters.")
    except Exception as e:
        st.error(f"An error occurred: {str(e)}")
        st.info("Please try refreshing the page or contact support if the problem persists.")

def analytics():
    set_custom_style()

    # Modern page header
    st.markdown("""
        <div style='text-align: center; margin-bottom: 2rem; padding: 2rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border-radius: 20px; color: white; box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);'>
            <h1 style='margin: 0; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;'>
                📈 Market Analytics
            </h1>
            <p style='margin: 0; font-size: 1.1rem; opacity: 0.9;'>
                Comprehensive market insights and advanced analytics
            </p>
        </div>
    """, unsafe_allow_html=True)

    data = get_sample_market_data()
    if data.empty:
        st.warning("No data available yet.")
        return

    # Add tabs for different analytics views
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📊 Overview", "📉 Price Analysis", "🗺️ Regional Analysis", "🔮 Future Predictions", "💡 Recommendations"])
    
    with tab1:
        st.subheader("Market Overview")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Products", len(data["Product"].unique()))
        with col2:
            st.metric("Total Locations", len(data["Location"].unique()))
        with col3:
            st.metric("Avg. Price (TZS)", f"{data['Price'].mean():.0f}")
        with col4:
            st.metric("Latest Data", data["Date"].max().strftime('%Y-%m-%d'))
        
        # Add a heatmap of prices by product and location
        st.subheader("Price Heatmap by Product and Location")
        pivot = data.pivot_table(index="Product", columns="Location", values="Price", aggfunc="mean")
        fig, ax = plt.subplots(figsize=(10, 6))
        sns.heatmap(pivot, annot=True, cmap="YlGnBu", fmt=".0f", ax=ax)
        plt.title("Average Price by Product and Location")
        st.pyplot(fig)
        plt.close()
        
    with tab2:
        st.subheader("Price Analysis")
        
        # Price trends over time
        st.subheader("Price Trends Over Time")
        fig = plt.figure(figsize=(10, 6))
        for product in data["Product"].unique():
            subset = data[data["Product"] == product]
            plt.plot(subset["Date"], subset["Price"], marker="o", label=product)
        plt.xlabel("Date")
        plt.ylabel("Price (TZS)")
        plt.title("Price Trends by Product")
        plt.legend()
        plt.grid(True)
        st.pyplot(fig)
        plt.close()
        
        # Price volatility
        st.subheader("Price Volatility")
        volatility = data.groupby("Product")["Price"].std().sort_values(ascending=False)
        fig, ax = plt.subplots(figsize=(10, 5))
        volatility.plot(kind="bar", ax=ax)
        plt.title("Price Volatility by Product (Standard Deviation)")
        plt.ylabel("Standard Deviation")
        plt.grid(axis="y")
        st.pyplot(fig)
        plt.close()
        
        # Price distribution by product
        st.subheader("Price Distribution by Product")
        fig = plt.figure(figsize=(10, 6))
        for i, product in enumerate(data["Product"].unique()):
            plt.subplot(1, len(data["Product"].unique()), i+1)
            subset = data[data["Product"] == product]
            plt.boxplot(subset["Price"])
            plt.title(product)
            plt.ylabel("Price (TZS)")
        plt.tight_layout()
        st.pyplot(fig)
        plt.close()
    
    with tab3:
        st.subheader("Regional Analysis")
        
        # Map visualization (placeholder - would need actual coordinates)
        st.subheader("Price Map by Location")
        st.info("This would show a map of Tanzania with price indicators by location. Requires geographic coordinates.")
        
        # Regional price comparison
        st.subheader("Regional Price Comparison")
        for product in data["Product"].unique():
            fig, ax = plt.subplots(figsize=(10, 5))
            subset = data[data["Product"] == product]
            regional_avg = subset.groupby("Location")["Price"].mean().sort_values()
            regional_avg.plot(kind="bar", ax=ax)
            plt.title(f"{product} - Average Price by Location")
            plt.ylabel("Price (TZS)")
            plt.grid(axis="y")
            st.pyplot(fig)
            plt.close()
        
        # Regional price trends
        st.subheader("Regional Price Trends")
        location = st.selectbox("Select Location", data["Location"].unique())
        fig = plt.figure(figsize=(10, 6))
        for product in data["Product"].unique():
            subset = data[(data["Product"] == product) & (data["Location"] == location)]
            if not subset.empty:
                plt.plot(subset["Date"], subset["Price"], marker="o", label=product)
        plt.xlabel("Date")
        plt.ylabel("Price (TZS)")
        plt.title(f"Price Trends in {location}")
        plt.legend()
        plt.grid(True)
        st.pyplot(fig)
        plt.close()
    
    with tab4:
        st.subheader("🔮 Future Market Price Predictions")
        st.write("Advanced ML-powered predictions for market prices in the coming weeks and months.")

        # Product and location selection for predictions
        col1, col2 = st.columns(2)
        with col1:
            pred_product = st.selectbox("Select Product for Prediction", data['Product'].unique(), key="pred_product")
        with col2:
            pred_location = st.selectbox("Select Location for Prediction", data['Location'].unique(), key="pred_location")

        # Prediction controls
        col1, col2, col3 = st.columns(3)
        with col1:
            pred_period = st.selectbox(
                "Prediction Period:",
                ["Next 2 Weeks", "Next Month", "Next 3 Months", "Next 6 Months"],
                key="analytics_pred_period"
            )
        with col2:
            pred_freq = st.selectbox(
                "Frequency:",
                ["Daily", "Weekly", "Bi-weekly"],
                index=1,
                key="analytics_pred_freq"
            )
        with col3:
            show_confidence = st.checkbox("Show Confidence Bands", value=True)

        # Generate predictions
        model, base_date, train_df = train_price_predictor(data, pred_product, pred_location)
        if model is not None:
            # Calculate prediction dates
            today = pd.Timestamp.now().date()

            if pred_period == "Next 2 Weeks":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=14)
            elif pred_period == "Next Month":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=30)
            elif pred_period == "Next 3 Months":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=90)
            else:  # Next 6 Months
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=180)

            # Set frequency
            freq_map = {"Daily": "D", "Weekly": "7D", "Bi-weekly": "14D"}
            freq = freq_map[pred_freq]

            # Generate prediction dates
            future_dates = pd.date_range(start=pred_start, end=pred_end, freq=freq)

            if len(future_dates) > 0:
                # Make predictions
                days_since_base = np.array([(pd.Timestamp(d) - pd.Timestamp(base_date)).days for d in future_dates]).reshape(-1, 1)
                preds = model.predict(days_since_base)

                # Create prediction summary
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Average Predicted Price", f"{preds.mean():.0f} TZS")
                with col2:
                    st.metric("Price Range", f"{preds.min():.0f} - {preds.max():.0f} TZS")
                with col3:
                    current_price = train_df["Price"].iloc[-1]
                    price_change = ((preds.mean() - current_price) / current_price) * 100
                    st.metric("Expected Change", f"{price_change:+.1f}%")

                # Visualization
                fig, ax = plt.subplots(figsize=(12, 8))

                # Plot historical data
                ax.plot(train_df["Date"], train_df["Price"], marker="o", linewidth=2, label="Historical Prices", color="blue")

                # Plot predictions
                ax.plot(future_dates, preds, marker="x", linestyle="--", linewidth=2, label="Predicted Prices", color="orange")

                # Add confidence bands if requested
                if show_confidence:
                    confidence_lower = preds * 0.9
                    confidence_upper = preds * 1.1
                    ax.fill_between(future_dates, confidence_lower, confidence_upper, alpha=0.2, color="orange", label="Confidence Band (±10%)")

                # Add today line
                ax.axvline(x=pd.Timestamp(today), color='red', linestyle=':', alpha=0.7, label='Today')

                ax.set_xlabel("Date")
                ax.set_ylabel("Price (TZS)")
                ax.set_title(f"{pred_product} Price Predictions - {pred_location} ({pred_period})")
                ax.legend()
                ax.grid(True, alpha=0.3)

                plt.tight_layout()
                st.pyplot(fig)
                plt.close()

                # Detailed predictions table
                pred_df = pd.DataFrame({
                    "Date": future_dates,
                    "Predicted Price (TZS)": preds.astype(int),
                    "Day of Week": [d.strftime('%A') for d in future_dates]
                })

                st.subheader("📅 Detailed Predictions")
                st.dataframe(pred_df, use_container_width=True)

                # Trading insights
                st.subheader("💡 Trading Insights")

                # Find best buy/sell dates
                best_buy_idx = np.argmin(preds)
                best_sell_idx = np.argmax(preds)

                best_buy_date = future_dates[best_buy_idx].strftime('%Y-%m-%d')
                best_buy_price = preds[best_buy_idx]

                best_sell_date = future_dates[best_sell_idx].strftime('%Y-%m-%d')
                best_sell_price = preds[best_sell_idx]

                col1, col2 = st.columns(2)
                with col1:
                    st.info(f"🟢 **Best time to BUY:** {best_buy_date} at {best_buy_price:.0f} TZS")
                with col2:
                    st.info(f"🔴 **Best time to SELL:** {best_sell_date} at {best_sell_price:.0f} TZS")

                # Profit potential
                profit_potential = best_sell_price - best_buy_price
                profit_percentage = (profit_potential / best_buy_price) * 100

                if profit_potential > 0:
                    st.success(f"💰 **Profit Potential:** {profit_potential:.0f} TZS ({profit_percentage:.1f}%) if you buy at the lowest and sell at the highest predicted prices.")
                else:
                    st.warning("⚠️ **Limited Profit Potential:** Prices are expected to remain relatively stable during this period.")

            else:
                st.info("No prediction dates available for the selected range.")
        else:
            st.warning("⚠️ Not enough historical data for reliable predictions. Please select a different product/location combination.")

    with tab5:
        st.subheader("📋 Market Recommendations")
        # Generate recommendations based on price trends
        
        for product in data["Product"].unique():
            st.subheader(f"{product} Recommendations")
            
            # Find best location to buy
            product_data = data[data["Product"] == product]
            latest_date = product_data["Date"].max()
            latest_prices = product_data[product_data["Date"] == latest_date]
            
            if not latest_prices.empty:
                best_location = latest_prices.loc[latest_prices["Price"].idxmin()]
                
                # Calculate price trend
                product_trend = product_data.sort_values("Date")
                if len(product_trend) >= 2:
                    first_price = product_trend.iloc[0]["Price"]
                    last_price = product_trend.iloc[-1]["Price"]
                    pct_change = ((last_price - first_price) / first_price) * 100
                    
                    trend_icon = "📈" if pct_change > 0 else "📉"
                    trend_color = "red" if pct_change > 0 else "green"
                    
                    st.markdown(f"""
                    <div style='background-color: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;'>
                        <h4>{product} - {trend_icon} <span style='color:{trend_color};'>{pct_change:.1f}%</span> over period</h4>
                        <p>Best place to buy: <b>{best_location['Location']}</b> at <b>{best_location['Price']} TZS</b></p>
                        <p>Recommendation: {
                            "Consider buying soon as prices are rising." if pct_change > 5 else
                            "Good time to buy, prices are falling." if pct_change < -5 else
                            "Prices are relatively stable."
                        }</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Price forecast
                    st.markdown("#### Price Forecast")
                    model, base_date, train_df = train_price_predictor(data, product, best_location['Location'])
                    if model is not None:
                        last_day = (train_df["Date"].max() - base_date).days
                        future_days = np.arange(last_day + 7, last_day + 29, 7)
                        future_dates = [base_date + pd.Timedelta(days=int(d)) for d in future_days]
                        preds = model.predict(future_days.reshape(-1, 1))
                        
                        # Determine if prices are expected to rise or fall
                        future_trend = "rise" if preds[-1] > preds[0] else "fall"
                        
                        st.markdown(f"Prices are expected to **{future_trend}** in the coming weeks.")
                        
                        # Plot forecast
                        fig, ax = plt.subplots(figsize=(10, 5))
                        ax.plot(train_df["Date"], train_df["Price"], marker="o", label="Historical")
                        ax.plot(future_dates, preds, marker="x", linestyle="--", label="Forecast")
                        ax.set_xlabel("Date")
                        ax.set_ylabel("Price (TZS)")
                        ax.set_title(f"{product} Price Forecast in {best_location['Location']}")
                        ax.legend()
                        ax.grid(True)
                        st.pyplot(fig)
                        plt.close()

# Email Alert Function
def send_email_alert(to_email, product, current_price, threshold, alert_type="below"):
    """Send email alert when price threshold is reached"""
    try:
        import smtplib
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart

        # Email configuration from secrets.toml
        try:
            smtp_server = st.secrets["email"]["smtp_server"]
            smtp_port = st.secrets["email"]["smtp_port"]
            sender_email = st.secrets["email"]["email_user"]
            sender_password = st.secrets["email"]["email_password"]
        except KeyError as e:
            st.error(f"❌ Email configuration missing: {e}")
            return False

        # Create message
        message = MIMEMultipart("alternative")
        message["From"] = sender_email
        message["To"] = to_email
        message["Subject"] = f"🔔 Smart Market Price Alert: {product}"

        # Create both plain text and HTML versions
        text_body = f"""
Smart Market Price Alert

Hello!

Your price alert for {product} has been triggered!

Current Price: {current_price:,.0f} TZS
Your Threshold: {threshold:,.0f} TZS
Alert Type: Price went {alert_type} threshold

Time: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

Best regards,
Smart Market Team

---
This is an automated message from Smart Market.
To manage your alerts, please log in to your account.
        """

        html_body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #3b82f6, #8b5cf6); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center; }}
                .content {{ background: #f8f9fa; padding: 20px; border-radius: 0 0 10px 10px; }}
                .alert-box {{ background: #fff; border-left: 4px solid #3b82f6; padding: 15px; margin: 15px 0; border-radius: 5px; }}
                .price {{ font-size: 24px; font-weight: bold; color: #e74c3c; }}
                .threshold {{ font-size: 18px; color: #27ae60; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔔 Smart Market Price Alert</h1>
                    <p>Your market intelligence companion</p>
                </div>
                <div class="content">
                    <h2>Alert Triggered for {product}</h2>
                    <div class="alert-box">
                        <p><strong>Product:</strong> {product}</p>
                        <p><strong>Current Price:</strong> <span class="price">{current_price:,.0f} TZS</span></p>
                        <p><strong>Your Threshold:</strong> <span class="threshold">{threshold:,.0f} TZS</span></p>
                        <p><strong>Alert Type:</strong> Price went {alert_type} threshold</p>
                        <p><strong>Time:</strong> {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    </div>
                    <p>This alert was triggered because the price has reached your specified threshold.
                    Consider taking action based on your trading strategy.</p>
                </div>
                <div class="footer">
                    <p>Best regards,<br>Smart Market Team</p>
                    <p><em>This is an automated message. To manage your alerts, please log in to your account.</em></p>
                </div>
            </div>
        </body>
        </html>
        """

        # Create MIMEText objects
        text_part = MIMEText(text_body, "plain")
        html_part = MIMEText(html_body, "html")

        # Add parts to message
        message.attach(text_part)
        message.attach(html_part)

        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(message)

        return True

    except Exception as e:
        print(f"Error sending email: {e}")
        st.error(f"❌ Failed to send email: {str(e)}")
        return False

# Check and trigger alerts
def check_price_alerts():
    """Check all active alerts and send notifications if triggered"""
    try:
        with get_connection() as conn:
            cur = conn.cursor()

            # Get all active alerts
            cur.execute("""
                SELECT pa.id, pa.username, pa.product, pa.location, pa.target_price,
                       pa.alert_type, u.email
                FROM price_alerts pa
                JOIN users u ON pa.username = u.username
                WHERE pa.is_active = true
            """)
            alerts = cur.fetchall()

            for alert in alerts:
                alert_id, username, product, location, target_price, alert_type, email = alert

                # Get current price
                if location:
                    cur.execute("""
                        SELECT price FROM market_data
                        WHERE product = %s AND location = %s
                        ORDER BY date DESC LIMIT 1
                    """, (product, location))
                else:
                    cur.execute("""
                        SELECT AVG(price) FROM market_data
                        WHERE product = %s
                        GROUP BY date ORDER BY date DESC LIMIT 1
                    """, (product,))

                current_price_result = cur.fetchone()
                if not current_price_result:
                    continue

                current_price = current_price_result[0]

                # Check if alert should trigger
                should_trigger = False
                if alert_type == "below" and current_price < target_price:
                    should_trigger = True
                elif alert_type == "above" and current_price > target_price:
                    should_trigger = True

                if should_trigger:
                    # Send email alert
                    success = send_email_alert(email, product, current_price, target_price, alert_type)

                    if success:
                        # Update last_checked timestamp
                        cur.execute("""
                            UPDATE price_alerts
                            SET last_checked = CURRENT_TIMESTAMP
                            WHERE id = %s
                        """, (alert_id,))
                        conn.commit()

                        # Optionally deactivate the alert after triggering
                        # cur.execute("UPDATE price_alerts SET is_active = false WHERE id = %s", (alert_id,))

            return True

    except Exception as e:
        print(f"Error checking alerts: {e}")
        return False

# Price Alerts System
def price_alerts_system():
    st.header("🔔 Price Alert Management")
    st.write("Set up and manage your price alerts to get notified when prices reach your target levels.")

    # Get user's email and phone from database
    with get_connection() as conn:
        cur = conn.cursor()
        cur.execute("SELECT email, phone FROM users WHERE username = %s", (st.session_state.username,))
        user_data = cur.fetchone()
        user_email = user_data[0] if user_data and user_data[0] else ""
        user_phone = user_data[1] if user_data and user_data[1] else ""

    # Create tabs for different alert functions
    tab1, tab2, tab3 = st.tabs(["➕ Create Alert", "📋 My Alerts", "📊 Alert History"])

    with tab1:
        st.subheader("Create New Price Alert")

        # Get available products and locations
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT DISTINCT product FROM market_data ORDER BY product")
            products = [row[0] for row in cur.fetchall()]
            cur.execute("SELECT DISTINCT location FROM market_data ORDER BY location")
            locations = [row[0] for row in cur.fetchall()]

        col1, col2 = st.columns(2)
        with col1:
            selected_product = st.selectbox("Select Product", products)
            alert_type = st.selectbox("Alert Type", ["below", "above"])
        with col2:
            selected_location = st.selectbox("Select Location", ["All Locations"] + locations)
            target_price = st.number_input("Target Price (TZS)", min_value=0.0, value=1000.0, step=10.0)

        # Notification preferences
        st.subheader("📢 Notification Preferences")
        col1, col2 = st.columns(2)

        with col1:
            notification_type = st.selectbox("Notification Method", ["email", "sms", "both"])
            email_for_alert = st.text_input("Email for Notifications", value=user_email)

        with col2:
            if notification_type in ["sms", "both"]:
                phone_for_alert = st.text_input("Phone for SMS Notifications", value=user_phone, placeholder="+**********")
                st.caption("📱 Include country code (e.g., +1 for US)")
            else:
                phone_for_alert = ""

        if st.button("Create Alert", type="primary"):
            # Validation based on notification type
            valid_notification = False
            if notification_type == "email" and email_for_alert:
                valid_notification = True
            elif notification_type == "sms" and phone_for_alert:
                valid_notification = True
            elif notification_type == "both" and email_for_alert and phone_for_alert:
                valid_notification = True

            if selected_product and target_price > 0 and valid_notification:
                try:
                    with get_connection() as conn:
                        cur = conn.cursor()
                        location_value = None if selected_location == "All Locations" else selected_location
                        cur.execute("""
                            INSERT INTO price_alerts (username, product, location, target_price, alert_type, email, phone, notification_type, is_active)
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """, (st.session_state.username, selected_product, location_value, target_price, alert_type, email_for_alert, phone_for_alert, notification_type, True))
                        conn.commit()
                    notification_methods = []
                    if notification_type in ["email", "both"]:
                        notification_methods.append("email")
                    if notification_type in ["sms", "both"]:
                        notification_methods.append("SMS")

                    st.success(f"✅ Alert created! You'll be notified when {selected_product} price goes {alert_type} {target_price} TZS")
                    st.info(f"📢 Notifications via: {', '.join(notification_methods)}")
                    st.rerun()
                except Exception as e:
                    st.error(f"❌ Error creating alert: {e}")
            else:
                if notification_type == "email":
                    st.warning("⚠️ Please provide an email address for notifications")
                elif notification_type == "sms":
                    st.warning("⚠️ Please provide a phone number for SMS notifications")
                elif notification_type == "both":
                    st.warning("⚠️ Please provide both email and phone number for notifications")
                else:
                    st.warning("⚠️ Please fill in all required fields")

    with tab2:
        st.subheader("My Active Alerts")

        # Get user's alerts
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("""
                SELECT id, product, location, target_price, alert_type, is_active, created_at
                FROM price_alerts
                WHERE username = %s
                ORDER BY created_at DESC
            """, (st.session_state.username,))
            alerts = cur.fetchall()

        if alerts:
            for alert in alerts:
                alert_id, product, location, target_price, alert_type, is_active, created_at = alert
                location_display = location if location else "All Locations"

                # Create alert card
                with st.container():
                    col1, col2, col3, col4 = st.columns([3, 2, 2, 1])

                    with col1:
                        status_icon = "🟢" if is_active else "🔴"
                        st.write(f"{status_icon} **{product}** in {location_display}")
                        st.caption(f"Created: {created_at.strftime('%Y-%m-%d %H:%M')}")

                    with col2:
                        st.write(f"Target: {target_price:,.0f} TZS")
                        st.caption(f"Alert when price goes {alert_type}")

                    with col3:
                        # Check current price and show status
                        with get_connection() as conn:
                            cur = conn.cursor()
                            if location:
                                cur.execute("""
                                    SELECT price FROM market_data
                                    WHERE product = %s AND location = %s
                                    ORDER BY date DESC LIMIT 1
                                """, (product, location))
                            else:
                                cur.execute("""
                                    SELECT AVG(price) FROM market_data
                                    WHERE product = %s
                                    GROUP BY date ORDER BY date DESC LIMIT 1
                                """, (product,))
                            current_price = cur.fetchone()

                        if current_price:
                            current_price = current_price[0]
                            st.write(f"Current: {current_price:,.0f} TZS")

                            # Check if alert should trigger
                            should_trigger = False
                            if alert_type == "below" and current_price < target_price:
                                should_trigger = True
                                st.caption("🚨 Alert triggered!")
                            elif alert_type == "above" and current_price > target_price:
                                should_trigger = True
                                st.caption("🚨 Alert triggered!")
                            else:
                                st.caption("⏳ Monitoring...")
                        else:
                            st.caption("No current data")

                    with col4:
                        # Toggle active/inactive
                        if st.button("🔄", key=f"toggle_{alert_id}", help="Toggle alert"):
                            with get_connection() as conn:
                                cur = conn.cursor()
                                cur.execute("""
                                    UPDATE price_alerts SET is_active = %s WHERE id = %s
                                """, (not is_active, alert_id))
                                conn.commit()
                            st.rerun()

                        # Delete alert
                        if st.button("🗑️", key=f"delete_{alert_id}", help="Delete alert"):
                            with get_connection() as conn:
                                cur = conn.cursor()
                                cur.execute("DELETE FROM price_alerts WHERE id = %s", (alert_id,))
                                conn.commit()
                            st.success("Alert deleted!")
                            st.rerun()

                st.divider()
        else:
            st.info("📭 No alerts created yet. Create your first alert in the 'Create Alert' tab!")

    with tab3:
        st.subheader("Alert Performance & History")

        # Get alert statistics
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("""
                SELECT
                    COUNT(*) as total_alerts,
                    COUNT(CASE WHEN is_active = true THEN 1 END) as active_alerts,
                    COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_alerts
                FROM price_alerts
                WHERE username = %s
            """, (st.session_state.username,))
            stats = cur.fetchone()

        if stats and stats[0] > 0:
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Total Alerts", stats[0])
            with col2:
                st.metric("Active Alerts", stats[1])
            with col3:
                st.metric("Inactive Alerts", stats[2])

            # Test alert functionality
            st.subheader("Test Alert System")
            test_email = st.text_input("Test Email", value=user_email)
            if st.button("Send Test Alert"):
                if test_email:
                    success = send_email_alert(
                        to_email=test_email,
                        product="Test Product",
                        current_price=1000,
                        threshold=1200
                    )
                    if success:
                        st.success("✅ Test alert sent successfully!")
                    else:
                        st.error("❌ Failed to send test alert. Check email configuration.")
                else:
                    st.warning("Please enter an email address")
        else:
            st.info("No alert history available yet.")

# Main
def main():
    set_custom_style()
    if not st.session_state.logged_in:
        login()
    else:
        # Removed preloader div for a cleaner dashboard
        choice = render_sidebar()

        # Add wrapper div
        st.markdown("""
            <div class="wrapper">
                <div class="content-wrapper">
        """, unsafe_allow_html=True)

        if choice == "MarketTracker":
            market_price_tracking()
        elif choice == "Analytics":
            analytics()
        elif choice == "PriceAlerts":
            price_alerts_system()
        elif choice == "Feedback":
            feedback_system()
        elif choice == "AdminDashboard":
            admin_dashboard()
        elif choice == "ChangePassword":
            change_password()
        elif choice == "Logout":
            logout()
            
        # Modern footer
        st.markdown("""
                </div>
                <footer class="main-footer">
                    <div style='display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;'>
                        <div>
                            <strong style='background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                                          -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                                          background-clip: text;'>
                                🏪 Smart Market
                            </strong>
                            <span style='margin-left: 0.5rem; color: #64748b;'>© 2024 All rights reserved.</span>
                        </div>
                        <div style='display: flex; align-items: center; gap: 1rem;'>
                            <span style='color: #64748b; font-size: 0.9rem;'>Version 2.0.0</span>
                            <span style='color: #64748b; font-size: 0.9rem;'>•</span>
                            <span style='color: #64748b; font-size: 0.9rem;'>Enhanced GUI</span>
                        </div>
                    </div>
                </footer>
            </div>
        """, unsafe_allow_html=True)


if __name__ == '__main__':
    main()







