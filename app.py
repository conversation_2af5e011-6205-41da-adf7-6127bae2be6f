import pandas as pd
import streamlit as st
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.pipeline import make_pipeline
import psycopg2
from psycopg2.extras import RealDictCursor
import bcrypt
from PIL import Image
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import time
import pyarrow as pa

def fix_arrow_display_issue(df):
    """Convert datetime columns to strings to avoid PyArrow conversion issues"""
    df_copy = df.copy()
    for col in df_copy.columns:
        if pd.api.types.is_datetime64_any_dtype(df_copy[col]):
            df_copy[col] = pd.to_datetime(df_copy[col]).dt.strftime('%Y-%m-%d %H:%M:%S')
        elif pd.api.types.is_object_dtype(df_copy[col]):
            # Try to convert object columns that might contain timestamps
            try:
                temp = pd.to_datetime(df_copy[col])
                if not temp.isna().all():  # If conversion was successful
                    df_copy[col] = temp.dt.strftime('%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                pass  # Keep original if conversion fails
    return df_copy

def set_custom_style():
    st.markdown("""
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        /* Global Styles */
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* Main Layout */
        .block-container {
            max-width: 1200px !important;
            padding-top: 1rem;
            padding-bottom: 2rem;
            padding-left: 2rem;
            padding-right: 2rem;
        }

        /* Hide Streamlit branding */
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        header {visibility: hidden;}

        /* Modern Sidebar */
        [data-testid="stSidebar"] {
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%) !important;
            border-right: none !important;
            box-shadow: 4px 0 20px rgba(0,0,0,0.1);
        }

        [data-testid="stSidebar"] > div:first-child {
            background: transparent !important;
        }

        /* Hide Streamlit sidebar elements */
        [data-testid="stSidebarHeader"] {
            display: none !important;
        }
        [data-testid="stSidebarCollapsedControl"] {
            display: none !important;
        }
        [data-testid="stLogoSpacer"] {
            display: none !important;
        }

        /* Sidebar Brand */
        .sidebar-brand h2 {
            color: #ffffff !important;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 700;
            font-size: 1.8rem;
            letter-spacing: -0.5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Sidebar User Panel */
        .user-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .user-panel .user-avatar {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 600;
            margin-right: 0.75rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .user-panel .user-info {
            flex: 1;
        }

        .user-panel .user-name {
            color: #ffffff !important;
            font-weight: 600;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .user-panel .user-role {
            color: rgba(255,255,255,0.8) !important;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        /* Modern Navigation Menu */
        .nav-menu {
            margin-top: 1rem;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255,255,255,0.9) !important;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1) !important;
            color: #ffffff !important;
            transform: translateX(4px);
        }

        .nav-link.active {
            background: rgba(255,255,255,0.2) !important;
            color: #ffffff !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .nav-icon {
            margin-right: 0.75rem;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        /* Modern Info Boxes */
        .info-box {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .info-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
        }

        .info-box:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .info-box-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .info-box-text {
            font-size: 0.9rem;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-box-number {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }
        /* Modern Card Styling */
        .card {
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.12);
        }

        .card-header {
            padding: 1.5rem 2rem 1rem 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid rgba(0,0,0,0.08);
            position: relative;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-body {
            padding: 2rem;
        }

        /* Modern Buttons */
        .stButton > button {
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 12px !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600 !important;
            font-size: 0.9rem !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
            text-transform: none !important;
            letter-spacing: 0.5px !important;
        }

        .stButton > button:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%) !important;
        }

        .stButton > button:active {
            transform: translateY(0px) !important;
        }

        /* Form Inputs */
        .stTextInput > div > div > input {
            border-radius: 12px !important;
            border: 2px solid #e2e8f0 !important;
            padding: 0.75rem 1rem !important;
            font-size: 0.9rem !important;
            transition: all 0.3s ease !important;
            background: #ffffff !important;
        }

        .stTextInput > div > div > input:focus {
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        .stSelectbox > div > div > div {
            border-radius: 12px !important;
            border: 2px solid #e2e8f0 !important;
            background: #ffffff !important;
        }

        .stNumberInput > div > div > input {
            border-radius: 12px !important;
            border: 2px solid #e2e8f0 !important;
            background: #ffffff !important;
        }
        /* Modern Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        /* Enhanced Tables */
        .dataframe {
            border-radius: 12px !important;
            overflow: hidden !important;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08) !important;
            border: 1px solid rgba(0,0,0,0.05) !important;
        }

        /* Modern Tabs */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
            background: #f8fafc;
            padding: 8px;
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .stTabs [data-baseweb="tab"] {
            background: transparent !important;
            border-radius: 8px !important;
            color: #64748b !important;
            font-weight: 500 !important;
            padding: 12px 20px !important;
            transition: all 0.3s ease !important;
        }

        .stTabs [aria-selected="true"] {
            background: linear-gradient(135deg, #3b82f6, #1e40af) !important;
            color: white !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
        }

        /* Modern Metrics */
        [data-testid="metric-container"] {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        [data-testid="metric-container"]:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        /* Success/Error Messages */
        .stSuccess {
            background: linear-gradient(135deg, #10b981, #059669) !important;
            color: white !important;
            border-radius: 12px !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
        }

        .stError {
            background: linear-gradient(135deg, #ef4444, #dc2626) !important;
            color: white !important;
            border-radius: 12px !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3) !important;
        }

        .stWarning {
            background: linear-gradient(135deg, #f59e0b, #d97706) !important;
            color: white !important;
            border-radius: 12px !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3) !important;
        }

        .stInfo {
            background: linear-gradient(135deg, #3b82f6, #1e40af) !important;
            color: white !important;
            border-radius: 12px !important;
            border: none !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
        }

        /* Modern Footer */
        .main-footer {
            text-align: center;
            color: #64748b;
            font-size: 0.9rem;
            margin-top: 3rem;
            padding: 2rem 0;
            border-top: 1px solid rgba(0,0,0,0.05);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px 20px 0 0;
        }

        /* Loading Spinner */
        .stSpinner > div {
            border-top-color: #3b82f6 !important;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .block-container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .info-box {
                padding: 1rem;
            }

            .card-body {
                padding: 1.5rem;
            }
        }
        </style>
    """, unsafe_allow_html=True)

def show_loading_animation(text="Loading..."):
    with st.spinner(text):
        # Simulate loading for demonstration
        time.sleep(0.5)

# DB Connection
def get_connection():
    return psycopg2.connect(
        host=st.secrets["database"]["host"],
        port=st.secrets["database"]["port"],
        dbname=st.secrets["database"]["dbname"],
        user=st.secrets["database"]["user"],
        password=st.secrets["database"]["password"]
    )

def load_market_data():
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT id, date, product, price, location FROM market_data ORDER BY date DESC")
            data = pd.DataFrame(cur.fetchall(), columns=['ID', 'Date', 'Product', 'Price', 'Location'])
            
            # Properly convert date column to pandas datetime
            data['Date'] = pd.to_datetime(data['Date'], errors='coerce')
            
            # Drop rows where date conversion failed
            data = data.dropna(subset=['Date'])
            
            # For display purposes, keep as datetime for now
            # We'll convert to string only when needed for specific displays
            
            return data
    except Exception as e:
        st.error(f"Error loading market data: {e}")
        return pd.DataFrame(columns=['ID', 'Date', 'Product', 'Price', 'Location'])

# --- Helper: Get all users as DataFrame ---
def get_all_users():
    import pandas as pd
    with get_connection() as conn:
        cur = conn.cursor()
        cur.execute("SELECT username, email, role FROM users ORDER BY username")
        users = cur.fetchall()
    return pd.DataFrame(users, columns=["Username", "Email", "Role"])

# Pagination Helper
def paginate_dataframe(df, items_per_page=10, key_prefix="pagination"):
    # Remove 'ID' column for display
    display_df = df.drop(columns=['ID'], errors='ignore').copy()

    # --- DataTables-like controls ---
    # Search/filter
    search_query = st.text_input("🔍 Search", value="", key=f"{key_prefix}_search")
    if search_query:
        mask = np.column_stack([
            display_df[col].astype(str).str.contains(search_query, case=False, na=False)
            for col in display_df
        ])
        display_df = display_df.loc[mask.any(axis=1)]

    # Sorting
    sort_col = st.selectbox("Sort by", display_df.columns, key=f"{key_prefix}_sort_col")
    sort_asc = st.radio("Order", ["Ascending", "Descending"], horizontal=True, key=f"{key_prefix}_sort_order")
    display_df = display_df.sort_values(by=sort_col, ascending=(sort_asc == "Ascending"))

    # Pagination
    total_rows = len(display_df)
    total_pages = max(1, (total_rows + items_per_page - 1) // items_per_page)
    page = st.number_input("Page", min_value=1, max_value=total_pages, value=1, step=1, key=f"{key_prefix}_page")
    start_idx = (page - 1) * items_per_page
    end_idx = min(start_idx + items_per_page, total_rows)
    paged_df = display_df.iloc[start_idx:end_idx]

    # Column sorting indicators
    styled_cols = [
        f"{col} {'🔼' if col == sort_col and sort_asc == 'Ascending' else ('🔽' if col == sort_col else '')}"
        for col in display_df.columns
    ]
    paged_df.columns = styled_cols

    # Show table and controls
    st.markdown(f"""
        <div class="datatable-controls">
            <div class="datatable-search">
                <b>Search:</b> <input value="{search_query}" style="margin-left:0.5rem;" readonly>
            </div>
            <div class="datatable-pagination">
                <b>Page:</b> {page} / {total_pages}
            </div>
        </div>
    """, unsafe_allow_html=True)
    st.dataframe(paged_df, use_container_width=True)

# Email Alert
def send_email_alert(to_email, product, current_price, threshold):
    try:
        smtp_server = st.secrets["email"]["smtp_server"]
        smtp_port = st.secrets["email"]["smtp_port"]
        email_user = st.secrets["email"]["email_user"]
        email_password = st.secrets["email"]["email_password"]

        msg = MIMEMultipart()
        msg["From"] = email_user
        msg["To"] = to_email
        msg["Subject"] = f"\U0001F514 Price Alert for {product}"

        body = f"The price for **{product}** has dropped to **{current_price}**, below your alert threshold of {threshold}."
        msg.attach(MIMEText(body, "plain"))

        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(email_user, email_password)
            server.send_message(msg)

        return True
    except Exception as e:
        st.error(f"Error sending email: {e}")
        return False

# Session Initialization
for key in ["logged_in", "username", "role", "confirm_delete_user", "confirm_delete_feedback", "confirm_delete_product", "current_page"]:
    if key not in st.session_state:
        if "confirm" in key:
            st.session_state[key] = {}
        elif key == "logged_in":
            st.session_state[key] = False
        elif key == "current_page":
            st.session_state[key] = "MarketTracker"
        else:
            st.session_state[key] = ""

# Authentication
def login():
    set_custom_style()

    # Modern login page layout
    st.markdown("""
        <div style='text-align: center; margin-bottom: 3rem;'>
            <h1 style='font-size: 3rem; font-weight: 700; background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                       -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                       background-clip: text; margin-bottom: 0.5rem;'>
                🏪 Smart Market
            </h1>
            <p style='font-size: 1.2rem; color: #64748b; font-weight: 500;'>
                Your intelligent companion for market price tracking and analysis
            </p>
        </div>
    """, unsafe_allow_html=True)

    col_features, col_form = st.columns([2, 1])

    with col_features:
        st.markdown("""
            <div style='background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                        padding: 2.5rem; border-radius: 20px;
                        margin-right: 2rem;
                        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
                        border: 1px solid rgba(0,0,0,0.05); height: 100%;'>
                <h3 style='color: #1e293b; margin-top: 0; font-weight: 600; margin-bottom: 2rem;'>
                    ✨ Platform Features
                </h3>
                <div style='margin: 1.5rem 0;'>
                    <div style='display: flex; align-items: center; margin-bottom: 1.5rem;
                                padding: 1rem; background: rgba(255,255,255,0.7); border-radius: 12px;'>
                        <div style='background: linear-gradient(135deg, #3b82f6, #1e40af); color: white;
                                    width: 48px; height: 48px; border-radius: 12px;
                                    display: flex; align-items: center; justify-content: center;
                                    margin-right: 1rem; font-size: 1.5rem;'>📊</div>
                        <div>
                            <div style='color: #1e293b; font-weight: 600; margin-bottom: 0.25rem;'>Real-time Price Tracking</div>
                            <div style='color: #64748b; font-size: 0.9rem;'>Monitor market prices across multiple locations</div>
                        </div>
                    </div>
                    <div style='display: flex; align-items: center; margin-bottom: 1.5rem;
                                padding: 1rem; background: rgba(255,255,255,0.7); border-radius: 12px;'>
                        <div style='background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;
                                    width: 48px; height: 48px; border-radius: 12px;
                                    display: flex; align-items: center; justify-content: center;
                                    margin-right: 1rem; font-size: 1.5rem;'>🔮</div>
                        <div>
                            <div style='color: #1e293b; font-weight: 600; margin-bottom: 0.25rem;'>AI-Powered Predictions</div>
                            <div style='color: #64748b; font-size: 0.9rem;'>Advanced ML models for future price forecasting</div>
                        </div>
                    </div>
                    <div style='display: flex; align-items: center; margin-bottom: 1.5rem;
                                padding: 1rem; background: rgba(255,255,255,0.7); border-radius: 12px;'>
                        <div style='background: linear-gradient(135deg, #06b6d4, #0891b2); color: white;
                                    width: 48px; height: 48px; border-radius: 12px;
                                    display: flex; align-items: center; justify-content: center;
                                    margin-right: 1rem; font-size: 1.5rem;'>🔔</div>
                        <div>
                            <div style='color: #1e293b; font-weight: 600; margin-bottom: 0.25rem;'>Smart Alerts</div>
                            <div style='color: #64748b; font-size: 0.9rem;'>Custom price alerts and notifications</div>
                        </div>
                    </div>
                    <div style='display: flex; align-items: center;
                                padding: 1rem; background: rgba(255,255,255,0.7); border-radius: 12px;'>
                        <div style='background: linear-gradient(135deg, #10b981, #059669); color: white;
                                    width: 48px; height: 48px; border-radius: 12px;
                                    display: flex; align-items: center; justify-content: center;
                                    margin-right: 1rem; font-size: 1.5rem;'>📈</div>
                        <div>
                            <div style='color: #1e293b; font-weight: 600; margin-bottom: 0.25rem;'>Comprehensive Analytics</div>
                            <div style='color: #64748b; font-size: 0.9rem;'>Detailed reports and market insights</div>
                        </div>
                    </div>
                </div>
            </div>
        """, unsafe_allow_html=True)

    with col_form:
        st.markdown("""
            <div style='background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                        padding: 2rem; border-radius: 20px;
                        margin-left: -1rem;
                        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
                        border: 1px solid rgba(0,0,0,0.05); height: 100%;'>
                <div style='text-align: center; margin-bottom: 2rem;'>
                    <h3 style='color: #1e293b; font-weight: 600; margin-bottom: 0.5rem;'>🔐 Sign In</h3>
                    <p style='color: #64748b; font-size: 0.9rem;'>Access your Smart Market dashboard</p>
                </div>
            </div>
        """, unsafe_allow_html=True)

        with st.container():
            st.markdown("<div style='margin-top: -2rem; padding: 0 2.5rem 2.5rem 2.5rem;'>", unsafe_allow_html=True)

            username = st.text_input(
                "👤 Username",
                placeholder="Enter your username",
                help="Enter your registered username"
            )
            password = st.text_input(
                "🔒 Password",
                type='password',
                placeholder="Enter your password",
                help="Enter your account password"
            )

            st.markdown("<br>", unsafe_allow_html=True)

            if st.button("🚀 Sign In", type="primary", use_container_width=True):
                if not username or not password:
                    st.error("❌ Please enter both username and password")
                else:
                    show_loading_animation("Signing in...")
                    with get_connection() as conn:
                        cur = conn.cursor(cursor_factory=RealDictCursor)
                        cur.execute("SELECT * FROM users WHERE username = %s", (username,))
                        user = cur.fetchone()
                        if user and bcrypt.checkpw(password.encode(), user['password'].encode()):
                            st.session_state.logged_in = True
                            st.session_state.username = username
                            st.session_state.role = user['role']
                            st.success("🎉 Welcome back! Redirecting to dashboard...")
                            st.rerun()
                        else:
                            st.error("❌ Invalid username or password")

            st.markdown("</div>", unsafe_allow_html=True)

        st.markdown("""
            <div style='text-align: center; margin-top: 2rem; padding: 1.5rem;
                        background: rgba(59, 130, 246, 0.05); border-radius: 12px;
                        border: 1px solid rgba(59, 130, 246, 0.1);'>
                <p style='color: #3b82f6; font-size: 0.9rem; margin: 0; font-weight: 500;'>
                    💡 Need access? Contact your administrator
                </p>
                <p style='color: #64748b; font-size: 0.8rem; margin: 0.5rem 0 0 0;'>
                    Demo credentials: admin/admin123 or user/user123
                </p>
            </div>
        """, unsafe_allow_html=True)

def logout():
    st.session_state.logged_in = False
    st.session_state.username = ''
    st.session_state.role = ''
    st.toast("You have been logged out.", icon="✅")
    st.rerun()

# Modern Sidebar
def render_sidebar():
    st.sidebar.markdown("""
        <div class='sidebar-brand'>
            <h2>🏪 Smart Market</h2>
        </div>

        <div class="user-panel">
            <div style="display: flex; align-items: center;">
                <div class="user-avatar">
                    {0}
                </div>
                <div class="user-info">
                    <div class="user-name">{1}</div>
                    <div class="user-role">{2}</div>
                </div>
            </div>
        </div>
    """.format(
        st.session_state.username[0].upper(),
        st.session_state.username,
        st.session_state.role.capitalize()
    ), unsafe_allow_html=True)

    # Modern navigation menu with emoji icons
    menu_items = {
        'admin': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "⚙️", "label": "Admin Dashboard", "id": "AdminDashboard"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ],
        'default': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "💬", "label": "Feedback", "id": "Feedback"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ]
    }

    items = menu_items['admin'] if st.session_state.role == 'admin' else menu_items['default']

    # Create modern navigation menu
    st.sidebar.markdown('<div class="nav-menu">', unsafe_allow_html=True)

    for item in items:
        if st.sidebar.button(
            f"{item['icon']} {item['label']}",
            key=f"nav_{item['id']}",
            use_container_width=True,
            help=item['label']
        ):
            # Update the current page in session state
            st.session_state.current_page = item['id']
            st.rerun()

    st.sidebar.markdown('</div>', unsafe_allow_html=True)

    # Return the current page from session state
    return st.session_state.current_page

# Admin Dashboard
def admin_dashboard():
    set_custom_style()

    # Modern admin dashboard header
    st.markdown("""
        <div style='text-align: center; margin-bottom: 2rem; padding: 2rem;
                    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
                    border-radius: 20px; color: white; box-shadow: 0 8px 32px rgba(124, 58, 237, 0.3);'>
            <h1 style='margin: 0; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;'>
                ⚙️ Admin Dashboard
            </h1>
            <p style='margin: 0; font-size: 1.1rem; opacity: 0.9;'>
                System administration and user management
            </p>
        </div>
    """, unsafe_allow_html=True)

    # Check if there's a specific section to show (from redirects)
    default_index = 0  # Default to User Management
    if "admin_dashboard_selector" in st.session_state:
        options = ["👥 User Management", "➕ Add New User", "💬 Feedback Management", "📦 Market Data Management"]
        if st.session_state.admin_dashboard_selector in options:
            default_index = options.index(st.session_state.admin_dashboard_selector)

    dashboard_option = st.selectbox(
        "Select Dashboard Section:",
        ["👥 User Management", "➕ Add New User", "💬 Feedback Management", "📦 Market Data Management"],
        index=default_index,
        key="admin_dashboard_selector"
    )

    st.markdown("---")

    if dashboard_option == "👥 User Management":
        admin_user_management()
    elif dashboard_option == "➕ Add New User":
        admin_add_user()
    elif dashboard_option == "💬 Feedback Management":
        admin_feedback_management()
    elif dashboard_option == "📦 Market Data Management":
        admin_market_data_management()

def admin_user_management():
    st.subheader("👥 User Management")
    st.write("Manage system users and their permissions")

    # Initialize session state for delete confirmations
    if 'delete_user_confirm' not in st.session_state:
        st.session_state.delete_user_confirm = {}

    user_df = get_all_users()
    if not user_df.empty:
        st.markdown("### Current Users")

        # Search functionality
        search_term = st.text_input("🔍 Search users", placeholder="Search by username, email, or role...")

        if search_term:
            mask = user_df.apply(lambda row: row.astype(str).str.contains(search_term, case=False).any(), axis=1)
            filtered_df = user_df[mask]
        else:
            filtered_df = user_df

        if filtered_df.empty:
            st.info("No users found matching your search.")
        else:
            # Display users in a modern card layout
            for idx, user in filtered_df.iterrows():
                username = user['Username']
                email = user['Email']
                role = user['Role']

                # Create user card
                with st.container():
                    st.markdown(f"""
                        <div style='background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                                    padding: 1.5rem; border-radius: 16px; margin-bottom: 1rem;
                                    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
                                    border: 1px solid rgba(0,0,0,0.05);'>
                            <div style='display: flex; align-items: center; justify-content: space-between;'>
                                <div style='display: flex; align-items: center;'>
                                    <div style='background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                                                color: white; width: 48px; height: 48px; border-radius: 12px;
                                                display: flex; align-items: center; justify-content: center;
                                                margin-right: 1rem; font-size: 1.2rem; font-weight: 600;'>
                                        {username[0].upper()}
                                    </div>
                                    <div>
                                        <div style='font-weight: 600; color: #1e293b; font-size: 1.1rem;'>{username}</div>
                                        <div style='color: #64748b; font-size: 0.9rem; margin: 0.25rem 0;'>{email}</div>
                                        <div style='display: inline-block; background: linear-gradient(135deg, #10b981, #059669);
                                                    color: white; padding: 0.25rem 0.75rem; border-radius: 20px;
                                                    font-size: 0.8rem; font-weight: 500; text-transform: uppercase;'>
                                            {role}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    """, unsafe_allow_html=True)

                    # Action buttons
                    col1, col2, col3 = st.columns([1, 1, 4])

                    with col1:
                        if st.button("✏️ Edit", key=f"edit_{username}", help=f"Edit {username}"):
                            st.info("Edit functionality coming soon!")

                    with col2:
                        # Prevent deleting the current user
                        if username == st.session_state.username:
                            st.button("🚫 Cannot Delete Self", key=f"nodelete_{username}", disabled=True)
                        else:
                            if st.button("🗑️ Delete", key=f"delete_{username}", help=f"Delete {username}"):
                                st.session_state.delete_user_confirm[username] = True

                    # Confirmation dialog
                    if st.session_state.delete_user_confirm.get(username, False):
                        st.warning(f"⚠️ Are you sure you want to delete user **{username}**? This action cannot be undone!")

                        col_confirm, col_cancel = st.columns(2)
                        with col_confirm:
                            if st.button("✅ Confirm Delete", key=f"confirm_delete_{username}", type="primary"):
                                try:
                                    with get_connection() as conn:
                                        cur = conn.cursor()
                                        cur.execute("DELETE FROM users WHERE username = %s", (username,))
                                        conn.commit()
                                        cur.close()

                                    st.success(f"🎉 User '{username}' has been deleted successfully!")
                                    st.session_state.delete_user_confirm[username] = False
                                    st.rerun()

                                except Exception as e:
                                    st.error(f"❌ Failed to delete user: {e}")

                        with col_cancel:
                            if st.button("❌ Cancel", key=f"cancel_delete_{username}"):
                                st.session_state.delete_user_confirm[username] = False
                                st.rerun()

                    st.markdown("---")

        # Summary
        st.markdown(f"""
            <div style='background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
                        color: white; padding: 1rem; border-radius: 12px; margin-top: 1rem;'>
                <strong>📊 Summary:</strong> {len(filtered_df)} users displayed
                {f"(filtered from {len(user_df)} total)" if search_term else ""}
            </div>
        """, unsafe_allow_html=True)

    else:
        st.info("No users found in the system.")

def admin_add_user():
    st.subheader("Add New User")
    st.write(f"Welcome, **{st.session_state.username}**! You can add new users to the system here.")

    # Use a form to prevent reruns/jumps on input change
    with st.form("add_user_form", clear_on_submit=True):
        st.write("**Fill out the form below:**")

        new_user = st.text_input("Username", help="Enter a unique username")
        new_email = st.text_input("Email Address", help="Enter a valid email address")
        new_password = st.text_input("Password", type='password', help="Enter a secure password")
        role = st.selectbox("Role", ['admin', 'consumer', 'traders', 'farmer'], help="Select user role")

        submitted = st.form_submit_button("Add User", type="primary")

        if submitted:
            if not new_user or not new_email or not new_password:
                st.warning("❌ All fields are required to add a new user.")
            elif not new_user.strip() or not new_email.strip() or not new_password.strip():
                st.warning("❌ All fields must contain valid data (no empty spaces).")
            else:
                try:
                    # Clean the input data
                    new_user = new_user.strip()
                    new_email = new_email.strip()

                    with get_connection() as conn:
                        cur = conn.cursor()
                        try:
                            # Check if user already exists
                            cur.execute("SELECT username FROM users WHERE username = %s", (new_user,))
                            existing_user = cur.fetchone()

                            if existing_user:
                                st.warning(f"❌ User '{new_user}' already exists!")
                            else:
                                # Hash the password
                                hashed_pw = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt()).decode()

                                # Insert new user
                                cur.execute(
                                    "INSERT INTO users (username, password, email, role) VALUES (%s, %s, %s, %s)",
                                    (new_user, hashed_pw, new_email, role)
                                )

                                # Commit the transaction
                                conn.commit()

                                st.success(f"🎉 User '{new_user}' added successfully!")
                                st.info("Redirecting to User Management to view the updated user list...")

                                # Update the admin dashboard selection to User Management
                                st.session_state.admin_dashboard_selector = "👥 User Management"

                                # Force a rerun to redirect
                                st.rerun()

                        except Exception as db_error:
                            # Rollback on error
                            conn.rollback()
                            st.error(f"❌ Database error: {db_error}")
                            import traceback
                            st.code(traceback.format_exc())
                            raise db_error
                        finally:
                            # Always close the cursor
                            cur.close()

                except Exception as e:
                    st.error(f"❌ Failed to add user: {e}")
                    import traceback
                    st.code(traceback.format_exc())

def admin_feedback_management():
    st.subheader("User Feedback")
    with get_connection() as conn:
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute("SELECT id, username, feedback, timestamp FROM feedback ORDER BY timestamp DESC")
        feedbacks = cur.fetchall()

    if feedbacks:
        for entry in feedbacks:
            cols = st.columns([5, 1])
            with cols[0]:
                st.markdown(f"**{entry['username']}** at {entry['timestamp']} said:")
                st.write(f"📣 _{entry['feedback']}_")
            if cols[1].button("❌", key=f"del_feedback_{entry['id']}"):
                st.session_state.confirm_delete_feedback[entry['id']] = True
            if st.session_state.confirm_delete_feedback.get(entry['id'], False):
                if st.checkbox("Confirm delete feedback?", key=f"confirm_feedback_{entry['id']}"):
                    with get_connection() as conn:
                        cur = conn.cursor()
                        cur.execute("DELETE FROM feedback WHERE id = %s", (entry['id'],))
                        conn.commit()
                        st.toast("Feedback deleted.", icon="✅")
                        st.rerun()
    else:
        st.info("No feedback submitted yet.")

def admin_market_data_management():
    st.subheader("Market Products")
    with get_connection() as conn:
        cur = conn.cursor()
        cur.execute("SELECT id, date, product, price, location FROM market_data ORDER BY date DESC")
        product_data = cur.fetchall()

    if product_data:
        product_df = pd.DataFrame(product_data, columns=['ID', 'Date', 'Product', 'Price', 'Location'])
        product_df['Date'] = pd.to_datetime(product_df['Date'], errors='coerce')
        product_df = product_df.dropna(subset=['Date'])

        # DataTables HTML/JS integration for large datasets
        columns = product_df.columns.tolist()
        html_code = f"""
        <div style='margin-bottom: 10px;'>
        <link rel='stylesheet' href='https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css'>
        <script src='https://code.jquery.com/jquery-3.6.0.min.js'></script>
        <script src='https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js'></script>
        <table id='productTable' class='display' style='width:100%'>
        <thead><tr>"""
        for col in columns:
            html_code += f"<th>{col}</th>"
        html_code += "<th>Actions</th></tr></thead><tbody>"
        for _, row in product_df.iterrows():
            html_code += "<tr>"
            for col in columns:
                html_code += f"<td>{row[col]}</td>"
            html_code += f"<td><button onclick=\"deleteProduct({row['ID']})\">Delete</button></td></tr>"
        html_code += """
        </tbody></table>
        <script>
        $(document).ready(function() {
            $('#productTable').DataTable({
                "paging": true,
                "pageLength": 10,
                "searching": true,
                "ordering": true,
                "info": true,
                "language": {
                    "paginate": {"previous": "&laquo;", "next": "&raquo;"},
                    "search": "🔍 Search:",
                    "lengthMenu": "Show _MENU_ entries"
                }
            });
        });
        function deleteProduct(productId) {
            alert('Delete functionality is not implemented in this demo. Product ID: ' + productId);
        }
        </script>
        </div>
        """
        st.components.v1.html(html_code, height=400)
    else:
        st.info("No market data available.")

# Change Password
def change_password():
    set_custom_style()

    # Modern page header
    st.markdown("""
        <div style='text-align: center; margin-bottom: 2rem; padding: 2rem;
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    border-radius: 20px; color: white; box-shadow: 0 8px 32px rgba(245, 158, 11, 0.3);'>
            <h1 style='margin: 0; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;'>
                🔐 Change Password
            </h1>
            <p style='margin: 0; font-size: 1.1rem; opacity: 0.9;'>
                Update your account password, {st.session_state.username}
            </p>
        </div>
    """, unsafe_allow_html=True)

    # Use a form to enable clearing after successful submission
    with st.form("change_password_form", clear_on_submit=True):
        st.write("**Enter your password details:**")

        current_pw = st.text_input("Current Password", type="password", help="Enter your current password")
        new_pw = st.text_input("New Password", type="password", help="Enter your new password")
        confirm_pw = st.text_input("Confirm New Password", type="password", help="Confirm your new password")

        submitted = st.form_submit_button("Update Password", type="primary")

        if submitted:
            if not current_pw or not new_pw or not confirm_pw:
                st.warning("❌ All fields are required.")
            elif len(new_pw) < 6:
                st.warning("❌ New password must be at least 6 characters long.")
            elif new_pw != confirm_pw:
                st.error("❌ New passwords do not match.")
            else:
                try:
                    with get_connection() as conn:
                        cur = conn.cursor()
                        cur.execute("SELECT password FROM users WHERE username = %s", (st.session_state.username,))
                        result = cur.fetchone()

                        if not result or not bcrypt.checkpw(current_pw.encode(), result[0].encode()):
                            st.error("❌ Current password is incorrect.")
                        else:
                            # Hash the new password
                            hashed_new_pw = bcrypt.hashpw(new_pw.encode(), bcrypt.gensalt()).decode()

                            # Update the password
                            cur.execute("UPDATE users SET password = %s WHERE username = %s", (hashed_new_pw, st.session_state.username))
                            conn.commit()

                            st.success("🎉 Password updated successfully!")
                            st.info("Redirecting to Admin Dashboard...")

                            # Redirect to Admin Dashboard
                            st.session_state.current_page = "AdminDashboard"

                            # Force a rerun to redirect
                            st.rerun()

                except Exception as e:
                    st.error(f"❌ Failed to update password: {e}")
                finally:
                    if 'cur' in locals():
                        cur.close()

# Feedback System
def feedback_system():
    # Feedback submission - only for non-admin users
    if st.session_state['role'] != 'admin':
        st.subheader("Feedback")
        feedback = st.text_area("Your feedback:")
        if st.button("Submit Feedback"):
            if feedback:
                with get_connection() as conn:
                    cur = conn.cursor()
                    cur.execute("INSERT INTO feedback (username, feedback) VALUES (%s, %s)", 
                                (st.session_state['username'], feedback))
                    conn.commit()
                st.success("Thank you for your feedback!")
            else:
                st.warning("Please enter some feedback before submitting.")


# --- Sample Data for Market Analysis in Tanzania ---
def get_sample_market_data():
    # Sample data for Tanzanian market analysis (Date, Product, Price, Location)
    data = [
        ["2024-01-01", "Maize", 1200, "Dar es Salaam"],
        ["2024-01-08", "Maize", 1250, "Dar es Salaam"],
        ["2024-01-15", "Maize", 1300, "Dar es Salaam"],
        ["2024-01-22", "Maize", 1280, "Dar es Salaam"],
        ["2024-01-29", "Maize", 1320, "Dar es Salaam"],
        ["2024-02-05", "Maize", 1350, "Dar es Salaam"],
        ["2024-01-01", "Rice", 2000, "Mwanza"],
        ["2024-01-08", "Rice", 2020, "Mwanza"],
        ["2024-01-15", "Rice", 2050, "Mwanza"],
        ["2024-01-22", "Rice", 2100, "Mwanza"],
        ["2024-01-29", "Rice", 2080, "Mwanza"],
        ["2024-02-05", "Rice", 2120, "Mwanza"],
        ["2024-01-01", "Beans", 1800, "Arusha"],
        ["2024-01-08", "Beans", 1820, "Arusha"],
        ["2024-01-15", "Beans", 1850, "Arusha"],
        ["2024-01-22", "Beans", 1870, "Arusha"],
        ["2024-01-29", "Beans", 1900, "Arusha"],
        ["2024-02-05", "Beans", 1920, "Arusha"],
    ]
    df = pd.DataFrame(data, columns=["Date", "Product", "Price", "Location"])
    df["Date"] = pd.to_datetime(df["Date"])
    return df

# --- Enhanced ML Model for Price Prediction ---
def train_price_predictor(df, product, location):
    """
    Enhanced price prediction model with multiple features and better accuracy
    """
    # Filter for product and location
    df = df[(df["Product"] == product) & (df["Location"] == location)].copy()
    df = df.sort_values("Date")

    if len(df) < 3:
        return None, None, None

    # Create enhanced features
    df["Days"] = (df["Date"] - df["Date"].min()).dt.days
    df["DayOfWeek"] = df["Date"].dt.dayofweek  # 0=Monday, 6=Sunday
    df["Month"] = df["Date"].dt.month
    df["Quarter"] = df["Date"].dt.quarter
    df["DayOfYear"] = df["Date"].dt.dayofyear

    # Add trend features
    df["Price_MA3"] = df["Price"].rolling(window=3, min_periods=1).mean()  # 3-period moving average
    df["Price_Lag1"] = df["Price"].shift(1).fillna(df["Price"].iloc[0])  # Previous price
    df["Price_Trend"] = df["Price"].diff().fillna(0)  # Price change from previous period

    # Add seasonal features
    df["Sin_DayOfYear"] = np.sin(2 * np.pi * df["DayOfYear"] / 365.25)
    df["Cos_DayOfYear"] = np.cos(2 * np.pi * df["DayOfYear"] / 365.25)
    df["Sin_Month"] = np.sin(2 * np.pi * df["Month"] / 12)
    df["Cos_Month"] = np.cos(2 * np.pi * df["Month"] / 12)

    # Prepare features
    feature_columns = [
        "Days", "DayOfWeek", "Month", "Quarter", "DayOfYear",
        "Price_MA3", "Price_Lag1", "Price_Trend",
        "Sin_DayOfYear", "Cos_DayOfYear", "Sin_Month", "Cos_Month"
    ]

    X = df[feature_columns].values
    y = df["Price"].values

    # Use an ensemble of models for better prediction

    # Create ensemble model
    models = {
        'rf': RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10),
        'gb': GradientBoostingRegressor(n_estimators=100, random_state=42, max_depth=6),
        'lr': LinearRegression()
    }

    # Scale features for linear regression
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # Train models
    trained_models = {}
    for name, model in models.items():
        if name == 'lr':
            model.fit(X_scaled, y)
        else:
            model.fit(X, y)
        trained_models[name] = model

    # Create ensemble predictor
    class EnsemblePredictor:
        def __init__(self, models, scaler, feature_columns, base_date, train_df):
            self.models = models
            self.scaler = scaler
            self.feature_columns = feature_columns
            self.base_date = base_date
            self.train_df = train_df
            self.last_price = train_df["Price"].iloc[-1]
            self.last_ma3 = train_df["Price_MA3"].iloc[-1]
            self.last_trend = train_df["Price_Trend"].iloc[-1]

        def predict(self, days_since_base):
            """Predict prices for given days since base date"""
            predictions = []

            for days in days_since_base.flatten():
                # Calculate date features
                pred_date = self.base_date + pd.Timedelta(days=int(days))

                # Create feature vector
                features = {
                    "Days": days,
                    "DayOfWeek": pred_date.dayofweek,
                    "Month": pred_date.month,
                    "Quarter": pred_date.quarter,
                    "DayOfYear": pred_date.dayofyear,
                    "Price_MA3": self.last_ma3,  # Use last known MA
                    "Price_Lag1": self.last_price,  # Use last known price
                    "Price_Trend": self.last_trend,  # Use last known trend
                    "Sin_DayOfYear": np.sin(2 * np.pi * pred_date.dayofyear / 365.25),
                    "Cos_DayOfYear": np.cos(2 * np.pi * pred_date.dayofyear / 365.25),
                    "Sin_Month": np.sin(2 * np.pi * pred_date.month / 12),
                    "Cos_Month": np.cos(2 * np.pi * pred_date.month / 12)
                }

                X_pred = np.array([features[col] for col in self.feature_columns]).reshape(1, -1)

                # Get predictions from all models
                rf_pred = self.models['rf'].predict(X_pred)[0]
                gb_pred = self.models['gb'].predict(X_pred)[0]
                lr_pred = self.models['lr'].predict(self.scaler.transform(X_pred))[0]

                # Ensemble prediction (weighted average)
                ensemble_pred = 0.4 * rf_pred + 0.4 * gb_pred + 0.2 * lr_pred

                # Update last values for next prediction
                self.last_price = ensemble_pred
                self.last_ma3 = (self.last_ma3 * 2 + ensemble_pred) / 3  # Simple MA update

                predictions.append(ensemble_pred)

            return np.array(predictions)

    # Create ensemble predictor
    ensemble_model = EnsemblePredictor(trained_models, scaler, feature_columns, df["Date"].min(), df)

    return ensemble_model, df["Date"].min(), df

# --- Market Price Tracker Section ---
def market_price_tracking():
    set_custom_style()

    # Modern page header
    st.markdown("""
        <div style='text-align: center; margin-bottom: 2rem; padding: 2rem;
                    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
                    border-radius: 20px; color: white; box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);'>
            <h1 style='margin: 0; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;'>
                📊 Market Price Tracker
            </h1>
            <p style='margin: 0; font-size: 1.1rem; opacity: 0.9;'>
                Real-time market analysis with AI-powered predictions
            </p>
        </div>
    """, unsafe_allow_html=True)

    try:
        # The model is trained on this sample data:
        data = get_sample_market_data()
        # Modern Dashboard Grid
        st.markdown('<div class="dashboard-grid">', unsafe_allow_html=True)
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.markdown(f"""
                <div class="info-box">
                    <span class="info-box-icon" style="color:#17a2b8;">📦</span>
                    <div class="info-box-text">Total Products</div>
                    <div class="info-box-number">{len(data['Product'].unique())}</div>
                </div>
            """, unsafe_allow_html=True)
        with col2:
            st.markdown(f"""
                <div class="info-box">
                    <span class="info-box-icon" style="color:#28a745;">📍</span>
                    <div class="info-box-text">Locations</div>
                    <div class="info-box-number">{len(data['Location'].unique())}</div>
                </div>
            """, unsafe_allow_html=True)
        with col3:
            st.markdown(f"""
                <div class="info-box">
                    <span class="info-box-icon" style="color:#ffc107;">💲</span>
                    <div class="info-box-text">Average Price</div>
                    <div class="info-box-number">${data['Price'].mean():.2f}</div>
                </div>
            """, unsafe_allow_html=True)
        with col4:
            st.markdown(f"""
                <div class="info-box">
                    <span class="info-box-icon" style="color:#dc3545;">⏰</span>
                    <div class="info-box-text">Latest Update</div>
                    <div class="info-box-number">{data['Date'].max().strftime('%Y-%m-%d')}</div>
                </div>
            """, unsafe_allow_html=True)
        st.markdown('</div>', unsafe_allow_html=True)

        # Filters Card
        with st.container():
            st.markdown("""
                <div class="card">
                    <div class="card-header">
                        <span class="card-title">Filter Data</span>
                    </div>
                    <div class="card-body">
            """, unsafe_allow_html=True)
            col1, col2, col3 = st.columns(3)
            with col1:
                product_filter = st.selectbox("Select Product", data['Product'].unique())
            with col2:
                location_filter = st.selectbox("Select Location", data['Location'].unique())
            with col3:
                min_date = data['Date'].min().date()
                max_date = data['Date'].max().date()
                today = data['Date'].max().date()
                # Allow picking dates up to 3 years in the future
                future_max = today.replace(year=today.year + 3)
                # Use two date_input widgets for start and end date (date picker)
                start_date = st.date_input(
                    "Start Date",
                    value=min_date,
                    min_value=min_date,
                    max_value=future_max
                )
                end_date = st.date_input(
                    "End Date",
                    value=today,
                    min_value=min_date,
                    max_value=future_max
                )
                if start_date > end_date:
                    st.warning("Start date cannot be after end date.")
            st.markdown("</div></div>", unsafe_allow_html=True)

        # Alert threshold input
        st.markdown("#### Set Price Alert")
        alert_email = st.text_input("Your Email for Alerts", value=st.session_state.get("username", ""))
        alert_threshold = st.number_input(
            f"Alert me when {product_filter} price in {location_filter} drops below (TZS):",
            min_value=0, value=1000, step=10, key="alert_threshold"
        )

        # Filtered data (only for actual data, not future)
        filtered_data = data[
            (data['Product'] == product_filter) &
            (data['Location'] == location_filter) &
            (data['Date'] >= pd.to_datetime(start_date)) &
            (data['Date'] <= pd.to_datetime(min(end_date, data['Date'].max().date())))
        ].copy()

        # Enhanced alert system for actual data
        if not filtered_data.empty and alert_email and alert_threshold > 0:
            below_actual = filtered_data[filtered_data["Price"] < alert_threshold]
            if not below_actual.empty:
                if st.button(f"Send Alert for Current Price Below {alert_threshold} TZS"):
                    sent = send_email_alert(
                        to_email=alert_email,
                        product=product_filter,
                        current_price=int(below_actual.iloc[0]["Price"]),
                        threshold=alert_threshold
                    )
                    if sent:
                        st.success(f"Alert sent to {alert_email} for {product_filter} below {alert_threshold} TZS (actual data).")
                    else:
                        st.error("Failed to send alert.")
            
            # Add automatic alert option
            auto_alert = st.checkbox("Enable automatic alerts when price reaches threshold", value=False)
            if auto_alert:
                st.info(f"Automatic alerts enabled. You will receive an email when {product_filter} price in {location_filter} drops below {alert_threshold} TZS.")
                # This would typically connect to a background process or scheduled task
                # For demo purposes, we'll simulate this with a button
                if st.button("Test automatic alert"):
                    sent = send_email_alert(
                        to_email=alert_email,
                        product=product_filter,
                        current_price=int(filtered_data["Price"].min()),
                        threshold=alert_threshold
                    )
                    if sent:
                        st.success(f"Test alert sent to {alert_email}.")
                    else:
                        st.error("Failed to send test alert.")

        # --- Enhanced ML Prediction Section ---
        st.subheader("🔮 Advanced Market Price Prediction")

        # Prediction time frame selection
        col1, col2 = st.columns(2)
        with col1:
            prediction_period = st.selectbox(
                "Select Prediction Period:",
                ["Next 2 Weeks", "Next Month", "Next 3 Months", "Next 6 Months", "Custom Range"],
                help="Choose how far into the future you want to predict prices"
            )

        with col2:
            prediction_frequency = st.selectbox(
                "Prediction Frequency:",
                ["Daily", "Weekly", "Bi-weekly", "Monthly"],
                index=1,  # Default to Weekly
                help="How often to show predictions within the selected period"
            )

        # Custom date range if selected
        if prediction_period == "Custom Range":
            col1, col2 = st.columns(2)
            with col1:
                custom_start = st.date_input(
                    "Prediction Start Date",
                    value=pd.Timestamp.now().date() + pd.Timedelta(days=1),
                    min_value=pd.Timestamp.now().date()
                )
            with col2:
                custom_end = st.date_input(
                    "Prediction End Date",
                    value=pd.Timestamp.now().date() + pd.Timedelta(days=30),
                    min_value=custom_start if 'custom_start' in locals() else pd.Timestamp.now().date()
                )

        model, base_date, train_df = train_price_predictor(data, product_filter, location_filter)
        if model is not None:
            # Calculate prediction dates based on selection
            today = pd.Timestamp.now().date()

            if prediction_period == "Next 2 Weeks":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=14)
            elif prediction_period == "Next Month":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=30)
            elif prediction_period == "Next 3 Months":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=90)
            elif prediction_period == "Next 6 Months":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=180)
            else:  # Custom Range
                pred_start = custom_start
                pred_end = custom_end

            # Set frequency
            freq_map = {
                "Daily": "D",
                "Weekly": "7D",
                "Bi-weekly": "14D",
                "Monthly": "30D"
            }
            freq = freq_map[prediction_frequency]

            # Generate prediction dates
            future_dates = pd.date_range(start=pred_start, end=pred_end, freq=freq)

            if len(future_dates) > 0:
                # Make predictions
                days_since_base = np.array([(pd.Timestamp(d) - pd.Timestamp(base_date)).days for d in future_dates]).reshape(-1, 1)
                preds = model.predict(days_since_base)

                # Create prediction dataframe
                pred_df = pd.DataFrame({
                    "Date": future_dates,
                    "Predicted Price (TZS)": preds.astype(int),
                    "Day of Week": [d.strftime('%A') for d in future_dates],
                    "Week of Year": [d.isocalendar()[1] for d in future_dates]
                })

                # Add price change indicators
                pred_df["Price Change"] = pred_df["Predicted Price (TZS)"].diff()
                pred_df["Trend"] = pred_df["Price Change"].apply(
                    lambda x: "📈 Rising" if x > 0 else "📉 Falling" if x < 0 else "➡️ Stable"
                )

                # Display prediction summary
                st.markdown("### 📊 Prediction Summary")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    avg_price = pred_df["Predicted Price (TZS)"].mean()
                    st.metric("Average Price", f"{avg_price:.0f} TZS")

                with col2:
                    min_price = pred_df["Predicted Price (TZS)"].min()
                    min_date = pred_df.loc[pred_df["Predicted Price (TZS)"].idxmin(), "Date"].strftime('%Y-%m-%d')
                    st.metric("Lowest Price", f"{min_price:.0f} TZS", f"on {min_date}")

                with col3:
                    max_price = pred_df["Predicted Price (TZS)"].max()
                    max_date = pred_df.loc[pred_df["Predicted Price (TZS)"].idxmax(), "Date"].strftime('%Y-%m-%d')
                    st.metric("Highest Price", f"{max_price:.0f} TZS", f"on {max_date}")

                with col4:
                    price_volatility = pred_df["Predicted Price (TZS)"].std()
                    st.metric("Price Volatility", f"{price_volatility:.0f} TZS")

                # Show detailed predictions table
                st.markdown("### 📅 Detailed Price Predictions")

                # Format the dataframe for better display
                display_df = pred_df.copy()
                display_df["Date"] = display_df["Date"].dt.strftime('%Y-%m-%d')
                display_df["Price Change"] = display_df["Price Change"].fillna(0).astype(int)

                st.dataframe(
                    display_df[["Date", "Day of Week", "Predicted Price (TZS)", "Price Change", "Trend"]],
                    use_container_width=True
                )

                # Enhanced visualization
                st.markdown("### 📈 Price Prediction Chart")

                fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

                # Main prediction chart
                ax1.plot(train_df["Date"], train_df["Price"], marker="o", linewidth=2, label="Historical Prices", color="blue")
                ax1.plot(pred_df["Date"], pred_df["Predicted Price (TZS)"], marker="x", linestyle="--", linewidth=2, label="Predicted Prices", color="orange")
                ax1.axvline(x=pd.Timestamp(today), color='red', linestyle=':', alpha=0.7, label='Today')
                ax1.fill_between(pred_df["Date"], pred_df["Predicted Price (TZS)"] * 0.95, pred_df["Predicted Price (TZS)"] * 1.05, alpha=0.2, color="orange", label="Confidence Band (±5%)")
                ax1.set_xlabel("Date")
                ax1.set_ylabel("Price (TZS)")
                ax1.set_title(f"{product_filter} Price Forecast - {location_filter} ({prediction_period})")
                ax1.legend()
                ax1.grid(True, alpha=0.3)

                # Price change chart
                colors = ['green' if x < 0 else 'red' if x > 0 else 'gray' for x in pred_df["Price Change"].fillna(0)]
                ax2.bar(pred_df["Date"], pred_df["Price Change"].fillna(0), color=colors, alpha=0.7)
                ax2.set_xlabel("Date")
                ax2.set_ylabel("Price Change (TZS)")
                ax2.set_title("Predicted Price Changes")
                ax2.grid(True, alpha=0.3)
                ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

                plt.tight_layout()
                st.pyplot(fig)
                plt.close()

                # Market insights and recommendations
                st.markdown("### 💡 Market Insights & Recommendations")

                # Calculate trends
                first_price = pred_df["Predicted Price (TZS)"].iloc[0]
                last_price = pred_df["Predicted Price (TZS)"].iloc[-1]
                overall_change = ((last_price - first_price) / first_price) * 100

                # Best times to buy/sell
                best_buy_idx = pred_df["Predicted Price (TZS)"].idxmin()
                best_sell_idx = pred_df["Predicted Price (TZS)"].idxmax()

                best_buy_date = pred_df.loc[best_buy_idx, "Date"].strftime('%Y-%m-%d')
                best_buy_price = pred_df.loc[best_buy_idx, "Predicted Price (TZS)"]

                best_sell_date = pred_df.loc[best_sell_idx, "Date"].strftime('%Y-%m-%d')
                best_sell_price = pred_df.loc[best_sell_idx, "Predicted Price (TZS)"]

                col1, col2 = st.columns(2)

                with col1:
                    trend_color = "green" if overall_change < 0 else "red" if overall_change > 0 else "gray"
                    trend_icon = "📉" if overall_change < 0 else "📈" if overall_change > 0 else "➡️"

                    st.markdown(f"""
                    <div style='background-color: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid {trend_color};'>
                        <h4>{trend_icon} Overall Trend</h4>
                        <p>Price is expected to <strong style='color: {trend_color};'>
                        {"decrease" if overall_change < 0 else "increase" if overall_change > 0 else "remain stable"}
                        </strong> by <strong>{abs(overall_change):.1f}%</strong> over the {prediction_period.lower()}.</p>
                        <p><strong>Best time to buy:</strong> {best_buy_date} at {best_buy_price} TZS</p>
                        <p><strong>Best time to sell:</strong> {best_sell_date} at {best_sell_price} TZS</p>
                    </div>
                    """, unsafe_allow_html=True)

                with col2:
                    # Trading recommendations
                    if overall_change < -5:
                        recommendation = "🟢 STRONG BUY - Prices expected to fall significantly"
                        rec_color = "green"
                    elif overall_change < -2:
                        recommendation = "🟡 BUY - Moderate price decrease expected"
                        rec_color = "orange"
                    elif overall_change > 5:
                        recommendation = "🔴 SELL - Prices expected to rise significantly"
                        rec_color = "red"
                    elif overall_change > 2:
                        recommendation = "🟡 HOLD/SELL - Moderate price increase expected"
                        rec_color = "orange"
                    else:
                        recommendation = "⚪ HOLD - Prices expected to remain stable"
                        rec_color = "gray"

                    st.markdown(f"""
                    <div style='background-color: #f8f9fa; padding: 1rem; border-radius: 8px; border-left: 4px solid {rec_color};'>
                        <h4>📋 Trading Recommendation</h4>
                        <p><strong>{recommendation}</strong></p>
                        <p>Volatility: <strong>{"High" if price_volatility > 100 else "Medium" if price_volatility > 50 else "Low"}</strong></p>
                        <p>Confidence: <strong>{"High" if len(train_df) > 10 else "Medium" if len(train_df) > 5 else "Low"}</strong></p>
                    </div>
                    """, unsafe_allow_html=True)

                # Alert logic with enhanced predictions
                if alert_email and alert_threshold > 0:
                    below_threshold = pred_df[pred_df["Predicted Price (TZS)"] < alert_threshold]
                    if not below_threshold.empty:
                        st.warning(f"⚠️ Price is predicted to drop below your threshold of {alert_threshold} TZS on {len(below_threshold)} occasions!")
                        if st.button("📧 Send Detailed Price Alert"):
                            # Send enhanced alert with prediction details
                            sent = send_email_alert(
                                to_email=alert_email,
                                product=product_filter,
                                current_price=int(below_threshold.iloc[0]["Predicted Price (TZS)"]),
                                threshold=alert_threshold
                            )
                            if sent:
                                st.success(f"📧 Detailed price alert sent to {alert_email}!")
                            else:
                                st.error("❌ Failed to send alert.")

            else:
                st.info("No prediction dates available for the selected range. Please adjust your selection.")
        else:
            st.info("⚠️ Not enough historical data for ML prediction. Please select a product/location with more data points.")

        if filtered_data.empty:
            st.warning("No data available for the selected filters.")
    except Exception as e:
        st.error(f"An error occurred: {str(e)}")
        st.info("Please try refreshing the page or contact support if the problem persists.")

def analytics():
    set_custom_style()

    # Modern page header
    st.markdown("""
        <div style='text-align: center; margin-bottom: 2rem; padding: 2rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    border-radius: 20px; color: white; box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);'>
            <h1 style='margin: 0; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;'>
                📈 Market Analytics
            </h1>
            <p style='margin: 0; font-size: 1.1rem; opacity: 0.9;'>
                Comprehensive market insights and advanced analytics
            </p>
        </div>
    """, unsafe_allow_html=True)

    data = get_sample_market_data()
    if data.empty:
        st.warning("No data available yet.")
        return

    # Add tabs for different analytics views
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📊 Overview", "📉 Price Analysis", "🗺️ Regional Analysis", "🔮 Future Predictions", "💡 Recommendations"])
    
    with tab1:
        st.subheader("Market Overview")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Products", len(data["Product"].unique()))
        with col2:
            st.metric("Total Locations", len(data["Location"].unique()))
        with col3:
            st.metric("Avg. Price (TZS)", f"{data['Price'].mean():.0f}")
        with col4:
            st.metric("Latest Data", data["Date"].max().strftime('%Y-%m-%d'))
        
        # Add a heatmap of prices by product and location
        st.subheader("Price Heatmap by Product and Location")
        pivot = data.pivot_table(index="Product", columns="Location", values="Price", aggfunc="mean")
        fig, ax = plt.subplots(figsize=(10, 6))
        sns.heatmap(pivot, annot=True, cmap="YlGnBu", fmt=".0f", ax=ax)
        plt.title("Average Price by Product and Location")
        st.pyplot(fig)
        plt.close()
        
    with tab2:
        st.subheader("Price Analysis")
        
        # Price trends over time
        st.subheader("Price Trends Over Time")
        fig = plt.figure(figsize=(10, 6))
        for product in data["Product"].unique():
            subset = data[data["Product"] == product]
            plt.plot(subset["Date"], subset["Price"], marker="o", label=product)
        plt.xlabel("Date")
        plt.ylabel("Price (TZS)")
        plt.title("Price Trends by Product")
        plt.legend()
        plt.grid(True)
        st.pyplot(fig)
        plt.close()
        
        # Price volatility
        st.subheader("Price Volatility")
        volatility = data.groupby("Product")["Price"].std().sort_values(ascending=False)
        fig, ax = plt.subplots(figsize=(10, 5))
        volatility.plot(kind="bar", ax=ax)
        plt.title("Price Volatility by Product (Standard Deviation)")
        plt.ylabel("Standard Deviation")
        plt.grid(axis="y")
        st.pyplot(fig)
        plt.close()
        
        # Price distribution by product
        st.subheader("Price Distribution by Product")
        fig = plt.figure(figsize=(10, 6))
        for i, product in enumerate(data["Product"].unique()):
            plt.subplot(1, len(data["Product"].unique()), i+1)
            subset = data[data["Product"] == product]
            plt.boxplot(subset["Price"])
            plt.title(product)
            plt.ylabel("Price (TZS)")
        plt.tight_layout()
        st.pyplot(fig)
        plt.close()
    
    with tab3:
        st.subheader("Regional Analysis")
        
        # Map visualization (placeholder - would need actual coordinates)
        st.subheader("Price Map by Location")
        st.info("This would show a map of Tanzania with price indicators by location. Requires geographic coordinates.")
        
        # Regional price comparison
        st.subheader("Regional Price Comparison")
        for product in data["Product"].unique():
            fig, ax = plt.subplots(figsize=(10, 5))
            subset = data[data["Product"] == product]
            regional_avg = subset.groupby("Location")["Price"].mean().sort_values()
            regional_avg.plot(kind="bar", ax=ax)
            plt.title(f"{product} - Average Price by Location")
            plt.ylabel("Price (TZS)")
            plt.grid(axis="y")
            st.pyplot(fig)
            plt.close()
        
        # Regional price trends
        st.subheader("Regional Price Trends")
        location = st.selectbox("Select Location", data["Location"].unique())
        fig = plt.figure(figsize=(10, 6))
        for product in data["Product"].unique():
            subset = data[(data["Product"] == product) & (data["Location"] == location)]
            if not subset.empty:
                plt.plot(subset["Date"], subset["Price"], marker="o", label=product)
        plt.xlabel("Date")
        plt.ylabel("Price (TZS)")
        plt.title(f"Price Trends in {location}")
        plt.legend()
        plt.grid(True)
        st.pyplot(fig)
        plt.close()
    
    with tab4:
        st.subheader("🔮 Future Market Price Predictions")
        st.write("Advanced ML-powered predictions for market prices in the coming weeks and months.")

        # Product and location selection for predictions
        col1, col2 = st.columns(2)
        with col1:
            pred_product = st.selectbox("Select Product for Prediction", data['Product'].unique(), key="pred_product")
        with col2:
            pred_location = st.selectbox("Select Location for Prediction", data['Location'].unique(), key="pred_location")

        # Prediction controls
        col1, col2, col3 = st.columns(3)
        with col1:
            pred_period = st.selectbox(
                "Prediction Period:",
                ["Next 2 Weeks", "Next Month", "Next 3 Months", "Next 6 Months"],
                key="analytics_pred_period"
            )
        with col2:
            pred_freq = st.selectbox(
                "Frequency:",
                ["Daily", "Weekly", "Bi-weekly"],
                index=1,
                key="analytics_pred_freq"
            )
        with col3:
            show_confidence = st.checkbox("Show Confidence Bands", value=True)

        # Generate predictions
        model, base_date, train_df = train_price_predictor(data, pred_product, pred_location)
        if model is not None:
            # Calculate prediction dates
            today = pd.Timestamp.now().date()

            if pred_period == "Next 2 Weeks":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=14)
            elif pred_period == "Next Month":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=30)
            elif pred_period == "Next 3 Months":
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=90)
            else:  # Next 6 Months
                pred_start = today + pd.Timedelta(days=1)
                pred_end = today + pd.Timedelta(days=180)

            # Set frequency
            freq_map = {"Daily": "D", "Weekly": "7D", "Bi-weekly": "14D"}
            freq = freq_map[pred_freq]

            # Generate prediction dates
            future_dates = pd.date_range(start=pred_start, end=pred_end, freq=freq)

            if len(future_dates) > 0:
                # Make predictions
                days_since_base = np.array([(pd.Timestamp(d) - pd.Timestamp(base_date)).days for d in future_dates]).reshape(-1, 1)
                preds = model.predict(days_since_base)

                # Create prediction summary
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Average Predicted Price", f"{preds.mean():.0f} TZS")
                with col2:
                    st.metric("Price Range", f"{preds.min():.0f} - {preds.max():.0f} TZS")
                with col3:
                    current_price = train_df["Price"].iloc[-1]
                    price_change = ((preds.mean() - current_price) / current_price) * 100
                    st.metric("Expected Change", f"{price_change:+.1f}%")

                # Visualization
                fig, ax = plt.subplots(figsize=(12, 8))

                # Plot historical data
                ax.plot(train_df["Date"], train_df["Price"], marker="o", linewidth=2, label="Historical Prices", color="blue")

                # Plot predictions
                ax.plot(future_dates, preds, marker="x", linestyle="--", linewidth=2, label="Predicted Prices", color="orange")

                # Add confidence bands if requested
                if show_confidence:
                    confidence_lower = preds * 0.9
                    confidence_upper = preds * 1.1
                    ax.fill_between(future_dates, confidence_lower, confidence_upper, alpha=0.2, color="orange", label="Confidence Band (±10%)")

                # Add today line
                ax.axvline(x=pd.Timestamp(today), color='red', linestyle=':', alpha=0.7, label='Today')

                ax.set_xlabel("Date")
                ax.set_ylabel("Price (TZS)")
                ax.set_title(f"{pred_product} Price Predictions - {pred_location} ({pred_period})")
                ax.legend()
                ax.grid(True, alpha=0.3)

                plt.tight_layout()
                st.pyplot(fig)
                plt.close()

                # Detailed predictions table
                pred_df = pd.DataFrame({
                    "Date": future_dates,
                    "Predicted Price (TZS)": preds.astype(int),
                    "Day of Week": [d.strftime('%A') for d in future_dates]
                })

                st.subheader("📅 Detailed Predictions")
                st.dataframe(pred_df, use_container_width=True)

                # Trading insights
                st.subheader("💡 Trading Insights")

                # Find best buy/sell dates
                best_buy_idx = np.argmin(preds)
                best_sell_idx = np.argmax(preds)

                best_buy_date = future_dates[best_buy_idx].strftime('%Y-%m-%d')
                best_buy_price = preds[best_buy_idx]

                best_sell_date = future_dates[best_sell_idx].strftime('%Y-%m-%d')
                best_sell_price = preds[best_sell_idx]

                col1, col2 = st.columns(2)
                with col1:
                    st.info(f"🟢 **Best time to BUY:** {best_buy_date} at {best_buy_price:.0f} TZS")
                with col2:
                    st.info(f"🔴 **Best time to SELL:** {best_sell_date} at {best_sell_price:.0f} TZS")

                # Profit potential
                profit_potential = best_sell_price - best_buy_price
                profit_percentage = (profit_potential / best_buy_price) * 100

                if profit_potential > 0:
                    st.success(f"💰 **Profit Potential:** {profit_potential:.0f} TZS ({profit_percentage:.1f}%) if you buy at the lowest and sell at the highest predicted prices.")
                else:
                    st.warning("⚠️ **Limited Profit Potential:** Prices are expected to remain relatively stable during this period.")

            else:
                st.info("No prediction dates available for the selected range.")
        else:
            st.warning("⚠️ Not enough historical data for reliable predictions. Please select a different product/location combination.")

    with tab5:
        st.subheader("📋 Market Recommendations")
        # Generate recommendations based on price trends
        
        for product in data["Product"].unique():
            st.subheader(f"{product} Recommendations")
            
            # Find best location to buy
            product_data = data[data["Product"] == product]
            latest_date = product_data["Date"].max()
            latest_prices = product_data[product_data["Date"] == latest_date]
            
            if not latest_prices.empty:
                best_location = latest_prices.loc[latest_prices["Price"].idxmin()]
                
                # Calculate price trend
                product_trend = product_data.sort_values("Date")
                if len(product_trend) >= 2:
                    first_price = product_trend.iloc[0]["Price"]
                    last_price = product_trend.iloc[-1]["Price"]
                    pct_change = ((last_price - first_price) / first_price) * 100
                    
                    trend_icon = "📈" if pct_change > 0 else "📉"
                    trend_color = "red" if pct_change > 0 else "green"
                    
                    st.markdown(f"""
                    <div style='background-color: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;'>
                        <h4>{product} - {trend_icon} <span style='color:{trend_color};'>{pct_change:.1f}%</span> over period</h4>
                        <p>Best place to buy: <b>{best_location['Location']}</b> at <b>{best_location['Price']} TZS</b></p>
                        <p>Recommendation: {
                            "Consider buying soon as prices are rising." if pct_change > 5 else
                            "Good time to buy, prices are falling." if pct_change < -5 else
                            "Prices are relatively stable."
                        }</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Price forecast
                    st.markdown("#### Price Forecast")
                    model, base_date, train_df = train_price_predictor(data, product, best_location['Location'])
                    if model is not None:
                        last_day = (train_df["Date"].max() - base_date).days
                        future_days = np.arange(last_day + 7, last_day + 29, 7)
                        future_dates = [base_date + pd.Timedelta(days=int(d)) for d in future_days]
                        preds = model.predict(future_days.reshape(-1, 1))
                        
                        # Determine if prices are expected to rise or fall
                        future_trend = "rise" if preds[-1] > preds[0] else "fall"
                        
                        st.markdown(f"Prices are expected to **{future_trend}** in the coming weeks.")
                        
                        # Plot forecast
                        fig, ax = plt.subplots(figsize=(10, 5))
                        ax.plot(train_df["Date"], train_df["Price"], marker="o", label="Historical")
                        ax.plot(future_dates, preds, marker="x", linestyle="--", label="Forecast")
                        ax.set_xlabel("Date")
                        ax.set_ylabel("Price (TZS)")
                        ax.set_title(f"{product} Price Forecast in {best_location['Location']}")
                        ax.legend()
                        ax.grid(True)
                        st.pyplot(fig)
                        plt.close()# Main
def main():
    set_custom_style()
    if not st.session_state.logged_in:
        login()
    else:
        # Removed preloader div for a cleaner dashboard
        choice = render_sidebar()

        # Add wrapper div
        st.markdown("""
            <div class="wrapper">
                <div class="content-wrapper">
        """, unsafe_allow_html=True)

        if choice == "MarketTracker":
            market_price_tracking()
        elif choice == "Analytics":
            analytics()
        elif choice == "Feedback":
            feedback_system()
        elif choice == "AdminDashboard":
            admin_dashboard()
        elif choice == "ChangePassword":
            change_password()
        elif choice == "Logout":
            logout()
            
        # Modern footer
        st.markdown("""
                </div>
                <footer class="main-footer">
                    <div style='display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;'>
                        <div>
                            <strong style='background: linear-gradient(135deg, #3b82f6, #8b5cf6);
                                          -webkit-background-clip: text; -webkit-text-fill-color: transparent;
                                          background-clip: text;'>
                                🏪 Smart Market
                            </strong>
                            <span style='margin-left: 0.5rem; color: #64748b;'>© 2024 All rights reserved.</span>
                        </div>
                        <div style='display: flex; align-items: center; gap: 1rem;'>
                            <span style='color: #64748b; font-size: 0.9rem;'>Version 2.0.0</span>
                            <span style='color: #64748b; font-size: 0.9rem;'>•</span>
                            <span style='color: #64748b; font-size: 0.9rem;'>Enhanced GUI</span>
                        </div>
                    </div>
                </footer>
            </div>
        """, unsafe_allow_html=True)


if __name__ == '__main__':
    main()







