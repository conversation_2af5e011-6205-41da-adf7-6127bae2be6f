#!/usr/bin/env python3
"""
Database Diagnostic Tool for Smart Market System
Run this script to diagnose database connection issues
"""

import psycopg2
import time
import sys
from psycopg2.extras import RealDictCursor

def test_connection_with_timeout(timeout=5):
    """Test database connection with specified timeout"""
    try:
        print(f"🔍 Testing connection with {timeout}s timeout...")
        
        conn = psycopg2.connect(
            host="localhost",
            port="5432", 
            dbname="market",
            user="postgres",
            password="alfred",
            connect_timeout=timeout
        )
        
        with conn:
            cur = conn.cursor()
            start_time = time.time()
            cur.execute("SELECT 1")
            query_time = time.time() - start_time
            
            print(f"✅ Connection successful!")
            print(f"   Query response time: {query_time:.3f}s")
            
        return True, query_time
        
    except psycopg2.OperationalError as e:
        if "timeout" in str(e).lower():
            print(f"❌ Connection timeout after {timeout}s")
            return False, "timeout"
        else:
            print(f"❌ Connection failed: {e}")
            return False, str(e)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False, str(e)

def test_database_tables():
    """Test if all required tables exist and are accessible"""
    try:
        print("\n🔍 Testing database tables...")
        
        with psycopg2.connect(
            host="localhost",
            port="5432",
            dbname="market", 
            user="postgres",
            password="alfred",
            connect_timeout=10
        ) as conn:
            cur = conn.cursor(cursor_factory=RealDictCursor)
            
            # Test each table
            tables = ["users", "market_data", "feedback", "price_alerts"]
            
            for table in tables:
                try:
                    start_time = time.time()
                    cur.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cur.fetchone()[0]
                    query_time = time.time() - start_time
                    
                    print(f"✅ Table '{table}': {count} records ({query_time:.3f}s)")
                    
                except Exception as e:
                    print(f"❌ Table '{table}': Error - {e}")
            
            # Test a sample login query
            print("\n🔍 Testing login query performance...")
            start_time = time.time()
            cur.execute("SELECT username, password, role FROM users WHERE username = %s", ("admin",))
            user = cur.fetchone()
            query_time = time.time() - start_time
            
            if user:
                print(f"✅ Login query successful ({query_time:.3f}s)")
                print(f"   Found user: {user['username']} with role: {user['role']}")
            else:
                print("❌ Admin user not found")
                
        return True
        
    except Exception as e:
        print(f"❌ Database table test failed: {e}")
        return False

def test_connection_performance():
    """Test connection performance with multiple attempts"""
    print("\n🔍 Testing connection performance (5 attempts)...")
    
    times = []
    successes = 0
    
    for i in range(5):
        print(f"   Attempt {i+1}/5...", end=" ")
        success, result = test_connection_with_timeout(10)
        
        if success:
            times.append(result)
            successes += 1
            print(f"✅ {result:.3f}s")
        else:
            print(f"❌ Failed")
    
    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"\n📊 Performance Summary:")
        print(f"   Success rate: {successes}/5 ({successes*20}%)")
        print(f"   Average time: {avg_time:.3f}s")
        print(f"   Min time: {min_time:.3f}s")
        print(f"   Max time: {max_time:.3f}s")
        
        if avg_time > 2:
            print("⚠️  Warning: Slow connection detected")
        elif avg_time > 5:
            print("❌ Critical: Very slow connection")
        else:
            print("✅ Connection performance is good")
    else:
        print("❌ All connection attempts failed")

def main():
    """Main diagnostic function"""
    print("🏪 Smart Market Database Diagnostic Tool")
    print("=" * 50)
    
    # Test basic connection
    print("1. Basic Connection Test")
    success, _ = test_connection_with_timeout(10)
    
    if not success:
        print("\n❌ Basic connection failed. Check:")
        print("   - PostgreSQL is running")
        print("   - Database 'market' exists")
        print("   - Username/password are correct")
        print("   - Host/port are accessible")
        return
    
    # Test database tables
    print("\n2. Database Tables Test")
    tables_ok = test_database_tables()
    
    if not tables_ok:
        print("\n❌ Database tables test failed")
        print("   - Run simple_db_init.py to initialize database")
        return
    
    # Test performance
    print("\n3. Connection Performance Test")
    test_connection_performance()
    
    print("\n" + "=" * 50)
    print("🎉 Diagnostic complete!")
    print("\n💡 Recommendations:")
    print("   - If connection is slow, check network/database load")
    print("   - If timeouts occur, increase timeout values")
    print("   - If tables are missing, run database initialization")
    print("   - Monitor database logs for additional insights")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Diagnostic interrupted by user")
    except Exception as e:
        print(f"\n❌ Diagnostic failed: {e}")
        sys.exit(1)
