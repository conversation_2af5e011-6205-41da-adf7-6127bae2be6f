-- Smart Market Database Setup Script
-- This script creates all necessary tables for the Smart Market system

-- Create database (run this separately if needed)
-- CREATE DATABASE market;

-- Connect to the market database
\c market;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create market_data table
CREATE TABLE IF NOT EXISTS market_data (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL,
    product VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price > 0),
    location VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create feedback table
CREATE TABLE IF NOT EXISTS feedback (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    feedback TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE
);

-- Create price_alerts table
CREATE TABLE IF NOT EXISTS price_alerts (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    product VARCHAR(100) NOT NULL,
    location VARCHAR(100),
    target_price DECIMAL(10,2) NOT NULL CHECK (target_price > 0),
    alert_type VARCHAR(20) DEFAULT 'below' CHECK (alert_type IN ('below', 'above')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (username) REFERENCES users(username) ON DELETE CASCADE
);

-- Add is_active column to price_alerts if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'price_alerts' AND column_name = 'is_active') THEN
        ALTER TABLE price_alerts ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_market_data_date ON market_data(date);
CREATE INDEX IF NOT EXISTS idx_market_data_product ON market_data(product);
CREATE INDEX IF NOT EXISTS idx_market_data_location ON market_data(location);
CREATE INDEX IF NOT EXISTS idx_market_data_product_location ON market_data(product, location);
CREATE INDEX IF NOT EXISTS idx_feedback_username ON feedback(username);
CREATE INDEX IF NOT EXISTS idx_feedback_timestamp ON feedback(timestamp);
CREATE INDEX IF NOT EXISTS idx_price_alerts_username ON price_alerts(username);
CREATE INDEX IF NOT EXISTS idx_price_alerts_product ON price_alerts(product);
CREATE INDEX IF NOT EXISTS idx_price_alerts_active ON price_alerts(is_active);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, password, email, role) 
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9jG', '<EMAIL>', 'admin')
ON CONFLICT (username) DO NOTHING;

-- Insert sample market data
INSERT INTO market_data (date, product, price, location) VALUES
('2024-01-01', 'Maize', 1200.00, 'Dar es Salaam'),
('2024-01-08', 'Maize', 1250.00, 'Dar es Salaam'),
('2024-01-15', 'Maize', 1300.00, 'Dar es Salaam'),
('2024-01-22', 'Maize', 1280.00, 'Dar es Salaam'),
('2024-01-29', 'Maize', 1320.00, 'Dar es Salaam'),
('2024-02-05', 'Maize', 1350.00, 'Dar es Salaam'),
('2024-01-01', 'Rice', 2000.00, 'Mwanza'),
('2024-01-08', 'Rice', 2020.00, 'Mwanza'),
('2024-01-15', 'Rice', 2050.00, 'Mwanza'),
('2024-01-22', 'Rice', 2100.00, 'Mwanza'),
('2024-01-29', 'Rice', 2080.00, 'Mwanza'),
('2024-02-05', 'Rice', 2120.00, 'Mwanza'),
('2024-01-01', 'Beans', 1800.00, 'Arusha'),
('2024-01-08', 'Beans', 1850.00, 'Arusha'),
('2024-01-15', 'Beans', 1900.00, 'Arusha'),
('2024-01-22', 'Beans', 1880.00, 'Arusha'),
('2024-01-29', 'Beans', 1920.00, 'Arusha'),
('2024-02-05', 'Beans', 1950.00, 'Arusha'),
('2024-01-01', 'Tomatoes', 800.00, 'Dodoma'),
('2024-01-08', 'Tomatoes', 850.00, 'Dodoma'),
('2024-01-15', 'Tomatoes', 900.00, 'Dodoma'),
('2024-01-22', 'Tomatoes', 880.00, 'Dodoma'),
('2024-01-29', 'Tomatoes', 920.00, 'Dodoma'),
('2024-02-05', 'Tomatoes', 950.00, 'Dodoma'),
('2024-01-01', 'Onions', 600.00, 'Mbeya'),
('2024-01-08', 'Onions', 620.00, 'Mbeya'),
('2024-01-15', 'Onions', 650.00, 'Mbeya'),
('2024-01-22', 'Onions', 640.00, 'Mbeya'),
('2024-01-29', 'Onions', 660.00, 'Mbeya'),
('2024-02-05', 'Onions', 680.00, 'Mbeya')
ON CONFLICT DO NOTHING;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_market_data_updated_at BEFORE UPDATE ON market_data FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions (adjust as needed)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

COMMIT;
