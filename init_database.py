#!/usr/bin/env python3
"""
Database initialization script for Smart Market system
This script creates the database and all required tables
"""

import psycopg2
import sys
import os
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def create_database():
    """Create the market database if it doesn't exist"""
    try:
        # Connect to PostgreSQL server (not to a specific database)
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            user="postgres",
            password="alfred"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cur = conn.cursor()
        
        # Check if database exists
        cur.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = 'market'")
        exists = cur.fetchone()
        
        if not exists:
            cur.execute('CREATE DATABASE market')
            print("✅ Database 'market' created successfully!")
        else:
            print("ℹ️  Database 'market' already exists.")
            
        cur.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        return False

def initialize_tables():
    """Initialize all tables and data"""
    try:
        # Connect to the market database
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            dbname="market",
            user="postgres",
            password="alfred"
        )
        cur = conn.cursor()
        
        # Read and execute the SQL setup script
        with open('database_setup.sql', 'r') as f:
            sql_script = f.read()
            
        # Remove the \c command as we're already connected to the right database
        sql_script = sql_script.replace('\\c market;', '')
        
        # Execute the script
        cur.execute(sql_script)
        conn.commit()
        
        print("✅ All tables and data initialized successfully!")
        
        # Verify tables were created
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        tables = cur.fetchall()
        
        print("\n📋 Created tables:")
        for table in tables:
            print(f"   - {table[0]}")
            
        # Check if admin user was created
        cur.execute("SELECT username, email, role FROM users WHERE role = 'admin'")
        admin_users = cur.fetchall()
        
        print("\n👤 Admin users:")
        for user in admin_users:
            print(f"   - {user[0]} ({user[1]}) - {user[2]}")
            
        # Check sample data
        cur.execute("SELECT COUNT(*) FROM market_data")
        data_count = cur.fetchone()[0]
        print(f"\n📊 Sample market data records: {data_count}")
        
        cur.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error initializing tables: {e}")
        return False

def test_connection():
    """Test database connection"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            dbname="market",
            user="postgres",
            password="alfred"
        )
        cur = conn.cursor()
        cur.execute("SELECT version()")
        version = cur.fetchone()
        print(f"✅ Database connection successful!")
        print(f"   PostgreSQL version: {version[0]}")
        
        cur.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def main():
    """Main initialization function"""
    print("🚀 Starting Smart Market Database Initialization...")
    print("=" * 50)
    
    # Step 1: Test initial connection
    print("\n1. Testing PostgreSQL connection...")
    if not test_connection():
        print("❌ Please ensure PostgreSQL is running and credentials are correct.")
        sys.exit(1)
    
    # Step 2: Create database
    print("\n2. Creating database...")
    if not create_database():
        print("❌ Failed to create database.")
        sys.exit(1)
    
    # Step 3: Initialize tables
    print("\n3. Initializing tables and data...")
    if not initialize_tables():
        print("❌ Failed to initialize tables.")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Database initialization completed successfully!")
    print("\n📝 Default admin credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("   Email: <EMAIL>")
    print("\n🔧 You can now run the Streamlit application with: streamlit run app.py")

if __name__ == "__main__":
    main()
