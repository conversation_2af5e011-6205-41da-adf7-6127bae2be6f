#!/usr/bin/env python3
"""
Login Fix Tool for Smart Market System
Run this script to fix common login issues
"""

import psycopg2
import bcrypt
import sys

def get_connection():
    """Get database connection"""
    return psycopg2.connect(
        host="localhost",
        port="5432",
        dbname="market",
        user="postgres", 
        password="alfred"
    )

def reset_admin_password():
    """Reset admin password to default"""
    try:
        print("🔧 Resetting admin password...")
        
        with get_connection() as conn:
            cur = conn.cursor()
            
            # Hash the default password
            hashed_pw = bcrypt.hashpw("admin123".encode(), bcrypt.gensalt()).decode()
            
            # Update admin password
            cur.execute("""
                UPDATE users 
                SET password = %s 
                WHERE username = 'admin'
            """, (hashed_pw,))
            
            if cur.rowcount > 0:
                conn.commit()
                print("✅ Admin password reset to 'admin123'")
                return True
            else:
                print("❌ Admin user not found")
                return False
                
    except Exception as e:
        print(f"❌ Failed to reset password: {e}")
        return False

def create_admin_user():
    """Create admin user if it doesn't exist"""
    try:
        print("🔧 Creating admin user...")
        
        with get_connection() as conn:
            cur = conn.cursor()
            
            # Check if admin exists
            cur.execute("SELECT username FROM users WHERE username = 'admin'")
            if cur.fetchone():
                print("ℹ️  Admin user already exists")
                return True
            
            # Hash the password
            hashed_pw = bcrypt.hashpw("admin123".encode(), bcrypt.gensalt()).decode()
            
            # Create admin user
            cur.execute("""
                INSERT INTO users (username, password, email, role)
                VALUES (%s, %s, %s, %s)
            """, ("admin", hashed_pw, "<EMAIL>", "admin"))
            
            conn.commit()
            print("✅ Admin user created successfully")
            print("   Username: admin")
            print("   Password: admin123")
            return True
            
    except Exception as e:
        print(f"❌ Failed to create admin user: {e}")
        return False

def create_test_user():
    """Create test user if it doesn't exist"""
    try:
        print("🔧 Creating test user...")
        
        with get_connection() as conn:
            cur = conn.cursor()
            
            # Check if user exists
            cur.execute("SELECT username FROM users WHERE username = 'user'")
            if cur.fetchone():
                print("ℹ️  Test user already exists")
                return True
            
            # Hash the password
            hashed_pw = bcrypt.hashpw("user123".encode(), bcrypt.gensalt()).decode()
            
            # Create test user
            cur.execute("""
                INSERT INTO users (username, password, email, role)
                VALUES (%s, %s, %s, %s)
            """, ("user", hashed_pw, "<EMAIL>", "user"))
            
            conn.commit()
            print("✅ Test user created successfully")
            print("   Username: user")
            print("   Password: user123")
            return True
            
    except Exception as e:
        print(f"❌ Failed to create test user: {e}")
        return False

def check_database_connection():
    """Check if database connection works"""
    try:
        print("🔍 Testing database connection...")
        
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT 1")
            
        print("✅ Database connection successful")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def list_users():
    """List all users in the database"""
    try:
        print("👥 Current users in database:")
        
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT username, email, role FROM users ORDER BY username")
            users = cur.fetchall()
            
            if users:
                print("   Username    | Email                | Role")
                print("   " + "-" * 45)
                for username, email, role in users:
                    print(f"   {username:<11} | {email:<20} | {role}")
            else:
                print("   No users found")
                
        return True
        
    except Exception as e:
        print(f"❌ Failed to list users: {e}")
        return False

def main():
    """Main function"""
    print("🏪 Smart Market Login Fix Tool")
    print("=" * 40)
    
    # Check database connection first
    if not check_database_connection():
        print("\n❌ Cannot connect to database. Please check:")
        print("   - PostgreSQL is running")
        print("   - Database 'market' exists")
        print("   - Connection credentials are correct")
        return
    
    print("\n1. Checking current users...")
    list_users()
    
    print("\n2. Ensuring admin user exists...")
    create_admin_user()
    
    print("\n3. Ensuring test user exists...")
    create_test_user()
    
    print("\n4. Resetting admin password...")
    reset_admin_password()
    
    print("\n" + "=" * 40)
    print("🎉 Login fix complete!")
    print("\n📝 You can now login with:")
    print("   Admin: username='admin', password='admin123'")
    print("   User:  username='user', password='user123'")
    print("\n💡 If you still have issues:")
    print("   - Run database_diagnostic.py for detailed analysis")
    print("   - Check network connectivity")
    print("   - Restart the Streamlit application")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Fix interrupted by user")
    except Exception as e:
        print(f"\n❌ Fix failed: {e}")
        sys.exit(1)
