#!/usr/bin/env python3
"""
Simple database initialization for Smart Market system
"""

import psycopg2
import bcrypt

def get_connection():
    """Get database connection"""
    return psycopg2.connect(
        host="localhost",
        port="5432",
        dbname="market",
        user="postgres",
        password="alfred"
    )

def create_tables():
    """Create all required tables"""
    with get_connection() as conn:
        cur = conn.cursor()
        
        # Create users table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create market_data table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS market_data (
                id SERIAL PRIMARY KEY,
                date DATE NOT NULL,
                product VARCHAR(100) NOT NULL,
                price DECIMAL(10,2) NOT NULL CHECK (price > 0),
                location VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create feedback table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS feedback (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                feedback TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create price_alerts table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS price_alerts (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) NOT NULL,
                product VARCHAR(100) NOT NULL,
                location VARCHAR(100),
                target_price DECIMAL(10,2) NOT NULL CHECK (target_price > 0),
                alert_type VARCHAR(20) DEFAULT 'below' CHECK (alert_type IN ('below', 'above')),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        print("✅ All tables created successfully!")

def insert_sample_data():
    """Insert sample data"""
    with get_connection() as conn:
        cur = conn.cursor()
        
        # Insert admin user if not exists
        hashed_password = bcrypt.hashpw("admin123".encode(), bcrypt.gensalt()).decode()
        cur.execute("""
            INSERT INTO users (username, password, email, role) 
            VALUES (%s, %s, %s, %s)
            ON CONFLICT (username) DO NOTHING
        """, ("admin", hashed_password, "<EMAIL>", "admin"))
        
        # Insert sample market data
        sample_data = [
            ('2024-01-01', 'Maize', 1200.00, 'Dar es Salaam'),
            ('2024-01-08', 'Maize', 1250.00, 'Dar es Salaam'),
            ('2024-01-15', 'Maize', 1300.00, 'Dar es Salaam'),
            ('2024-01-22', 'Maize', 1280.00, 'Dar es Salaam'),
            ('2024-01-29', 'Maize', 1320.00, 'Dar es Salaam'),
            ('2024-02-05', 'Maize', 1350.00, 'Dar es Salaam'),
            ('2024-01-01', 'Rice', 2000.00, 'Mwanza'),
            ('2024-01-08', 'Rice', 2020.00, 'Mwanza'),
            ('2024-01-15', 'Rice', 2050.00, 'Mwanza'),
            ('2024-01-22', 'Rice', 2100.00, 'Mwanza'),
            ('2024-01-29', 'Rice', 2080.00, 'Mwanza'),
            ('2024-02-05', 'Rice', 2120.00, 'Mwanza'),
            ('2024-01-01', 'Beans', 1800.00, 'Arusha'),
            ('2024-01-08', 'Beans', 1850.00, 'Arusha'),
            ('2024-01-15', 'Beans', 1900.00, 'Arusha'),
            ('2024-01-22', 'Beans', 1880.00, 'Arusha'),
            ('2024-01-29', 'Beans', 1920.00, 'Arusha'),
            ('2024-02-05', 'Beans', 1950.00, 'Arusha'),
            ('2024-01-01', 'Tomatoes', 800.00, 'Dodoma'),
            ('2024-01-08', 'Tomatoes', 850.00, 'Dodoma'),
            ('2024-01-15', 'Tomatoes', 900.00, 'Dodoma'),
            ('2024-01-22', 'Tomatoes', 880.00, 'Dodoma'),
            ('2024-01-29', 'Tomatoes', 920.00, 'Dodoma'),
            ('2024-02-05', 'Tomatoes', 950.00, 'Dodoma'),
            ('2024-01-01', 'Onions', 600.00, 'Mbeya'),
            ('2024-01-08', 'Onions', 620.00, 'Mbeya'),
            ('2024-01-15', 'Onions', 650.00, 'Mbeya'),
            ('2024-01-22', 'Onions', 640.00, 'Mbeya'),
            ('2024-01-29', 'Onions', 660.00, 'Mbeya'),
            ('2024-02-05', 'Onions', 680.00, 'Mbeya')
        ]
        
        for data in sample_data:
            cur.execute("""
                INSERT INTO market_data (date, product, price, location) 
                VALUES (%s, %s, %s, %s)
                ON CONFLICT DO NOTHING
            """, data)
        
        conn.commit()
        print("✅ Sample data inserted successfully!")

def main():
    """Main function"""
    try:
        print("🚀 Initializing Smart Market Database...")
        create_tables()
        insert_sample_data()
        
        # Test connection
        with get_connection() as conn:
            cur = conn.cursor()
            cur.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
            admin_count = cur.fetchone()[0]
            cur.execute("SELECT COUNT(*) FROM market_data")
            data_count = cur.fetchone()[0]
            
        print(f"✅ Database initialized successfully!")
        print(f"   Admin users: {admin_count}")
        print(f"   Market data records: {data_count}")
        print("\n📝 Default admin credentials:")
        print("   Username: admin")
        print("   Password: admin123")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
