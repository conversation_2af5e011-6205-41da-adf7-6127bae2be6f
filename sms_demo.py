#!/usr/bin/env python3
"""
Simple SMS demo using Twilio for Smart Market
"""

from twilio.rest import Client

# Twilio credentials
ACCOUNT_SID = "**********************************"
AUTH_TOKEN = "31ba45ca36196cab06908cc6230347a1"
FROM_PHONE = "+***********"

def send_demo_sms():
    """Send a demo SMS"""
    print("📱 Smart Market SMS Demo")
    print("=" * 30)
    
    # Get phone number from user
    to_phone = input("Enter your phone number (with country code, e.g., +**********): ").strip()
    
    if not to_phone:
        print("❌ No phone number provided")
        return
    
    try:
        # Create Twilio client
        client = Client(ACCOUNT_SID, AUTH_TOKEN)
        
        # Demo message
        message_body = """
🎉 Smart Market SMS Demo

Hello! This is a demo SMS from Smart Market.

✅ SMS notifications are working!

Features:
• Price alerts via SMS
• Welcome messages
• System notifications
• Real-time updates

Smart Market Team
        """.strip()
        
        print(f"\n📤 Sending demo SMS to {to_phone}...")
        
        # Send SMS
        message = client.messages.create(
            body=message_body,
            from_=FROM_PHONE,
            to=to_phone
        )
        
        print(f"✅ SMS sent successfully!")
        print(f"   Message SID: {message.sid}")
        print(f"   Status: {message.status}")
        print(f"   To: {to_phone}")
        print(f"   From: {FROM_PHONE}")
        
        print(f"\n📱 Check your phone for the SMS message!")
        
    except Exception as e:
        print(f"❌ Failed to send SMS: {e}")
        print("\n💡 Possible issues:")
        print("   - Invalid phone number format")
        print("   - Twilio account issues")
        print("   - Network connectivity")

if __name__ == "__main__":
    send_demo_sms()
