#!/usr/bin/env python3
"""
Test script to verify login redirection works correctly for different user roles
"""

import psycopg2
import bcrypt
import time

def get_connection():
    """Get database connection"""
    return psycopg2.connect(
        host="localhost",
        port="5432",
        dbname="market",
        user="postgres",
        password="alfred"
    )

def simulate_login_flow(username, password):
    """Simulate the login flow and return expected redirection"""
    print(f"\n🔍 Simulating login for: {username}")
    
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            
            # Simulate the exact query from app.py
            cur.execute("SELECT username, password, role FROM users WHERE username = %s", (username,))
            user = cur.fetchone()
            
            if user:
                db_username, db_password, db_role = user
                print(f"   Found user: {db_username} with role: {db_role}")
                
                # Verify password (same logic as app.py)
                if bcrypt.checkpw(password.encode(), db_password.encode()):
                    print(f"   ✅ Password verification: SUCCESS")
                    
                    # Determine expected redirection based on role
                    if db_role == 'admin':
                        expected_page = "AdminDashboard"
                        expected_message = "Welcome back Admin! Redirecting to Admin Dashboard..."
                    else:
                        expected_page = "MarketTracker"
                        expected_message = "Welcome back! Redirecting to Market Tracker..."
                    
                    print(f"   📍 Expected redirection: {expected_page}")
                    print(f"   💬 Expected message: {expected_message}")
                    
                    return {
                        "success": True,
                        "role": db_role,
                        "expected_page": expected_page,
                        "expected_message": expected_message
                    }
                else:
                    print(f"   ❌ Password verification: FAILED")
                    return {"success": False, "error": "Invalid password"}
            else:
                print(f"   ❌ User not found")
                return {"success": False, "error": "User not found"}
                
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return {"success": False, "error": str(e)}

def test_menu_items_by_role():
    """Test that menu items are correct for each role"""
    print("\n🧪 Testing Menu Items by Role")
    print("=" * 50)
    
    # Menu items from app.py
    menu_items = {
        'admin': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "⚙️", "label": "Admin Dashboard", "id": "AdminDashboard"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ],
        'user': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "🔔", "label": "Price Alerts", "id": "PriceAlerts"},
            {"icon": "💬", "label": "Feedback", "id": "Feedback"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ]
    }
    
    for role, items in menu_items.items():
        print(f"\n🔍 Menu for role: {role}")
        print(f"   Available items: {len(items)}")
        
        for item in items:
            print(f"   {item['icon']} {item['label']} ({item['id']})")
        
        # Check for admin-specific items
        admin_items = [item for item in items if item['id'] == 'AdminDashboard']
        if role == 'admin':
            if admin_items:
                print("   ✅ Admin Dashboard: Available (correct)")
            else:
                print("   ❌ Admin Dashboard: Missing (incorrect)")
        else:
            if not admin_items:
                print("   ✅ Admin Dashboard: Not available (correct)")
            else:
                print("   ❌ Admin Dashboard: Available (incorrect)")

def test_session_initialization():
    """Test session initialization logic"""
    print("\n🧪 Testing Session Initialization")
    print("=" * 50)
    
    # Simulate session initialization
    session_keys = {
        "logged_in": False,
        "username": "",
        "role": "",
        "current_page": None,  # Should be None initially
        "confirm_delete_user": {},
        "confirm_delete_feedback": {},
        "confirm_delete_product": {},
        "login_attempts": 0,
        "last_login_attempt": None
    }
    
    print("📋 Initial session state:")
    for key, value in session_keys.items():
        print(f"   {key}: {value}")
    
    # Test fallback logic for current_page
    print("\n🔍 Testing current_page fallback logic:")
    
    test_cases = [
        {"role": "admin", "current_page": None, "expected": "AdminDashboard"},
        {"role": "user", "current_page": None, "expected": "MarketTracker"},
        {"role": "admin", "current_page": "Analytics", "expected": "Analytics"},
        {"role": "user", "current_page": "PriceAlerts", "expected": "PriceAlerts"}
    ]
    
    for case in test_cases:
        role = case["role"]
        current_page = case["current_page"]
        expected = case["expected"]
        
        # Simulate the fallback logic from render_sidebar
        if current_page is None:
            if role == 'admin':
                result = "AdminDashboard"
            else:
                result = "MarketTracker"
        else:
            result = current_page
        
        if result == expected:
            print(f"   ✅ Role: {role}, current_page: {current_page} → {result} (correct)")
        else:
            print(f"   ❌ Role: {role}, current_page: {current_page} → {result} (expected: {expected})")

def main():
    """Main test function"""
    print("🏪 Smart Market Login Redirection Testing")
    print("=" * 60)
    
    # Test login flows
    print("\n🧪 Testing Login Flows")
    print("=" * 50)
    
    test_users = [
        {"username": "admin", "password": "admin123"},
        {"username": "user", "password": "user123"},
        {"username": "nonexistent", "password": "wrong"},
        {"username": "admin", "password": "wrongpassword"}
    ]
    
    for test_user in test_users:
        result = simulate_login_flow(test_user["username"], test_user["password"])
        
        if result["success"]:
            print(f"   ✅ Login successful for {test_user['username']}")
            print(f"      Role: {result['role']}")
            print(f"      Redirection: {result['expected_page']}")
        else:
            print(f"   ❌ Login failed for {test_user['username']}: {result['error']}")
    
    # Test menu items
    test_menu_items_by_role()
    
    # Test session initialization
    test_session_initialization()
    
    print("\n" + "=" * 60)
    print("🎉 Login redirection testing complete!")
    print("\n📝 Expected Behavior:")
    print("   - Admin login → Admin Dashboard")
    print("   - User login → Market Tracker")
    print("   - Admin sees Admin Dashboard in menu")
    print("   - User sees Price Alerts and Feedback in menu")
    print("   - Both see Market Tracker and Analytics")
    print("\n💡 Test in browser:")
    print("   1. Login as admin (admin/admin123)")
    print("   2. Should redirect to Admin Dashboard")
    print("   3. Logout and login as user (user/user123)")
    print("   4. Should redirect to Market Tracker")

if __name__ == "__main__":
    main()
