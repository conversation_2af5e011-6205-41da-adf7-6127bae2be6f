#!/usr/bin/env python3
"""
Test script to verify user roles and menu access
"""

import psycopg2
import bcrypt

def get_connection():
    """Get database connection"""
    return psycopg2.connect(
        host="localhost",
        port="5432",
        dbname="market",
        user="postgres",
        password="alfred"
    )

def test_user_authentication():
    """Test user authentication for different roles"""
    print("🧪 Testing User Authentication and Roles")
    print("=" * 50)
    
    test_users = [
        {"username": "admin", "password": "admin123", "expected_role": "admin"},
        {"username": "user", "password": "user123", "expected_role": "user"}
    ]
    
    try:
        with get_connection() as conn:
            cur = conn.cursor()
            
            for test_user in test_users:
                username = test_user["username"]
                password = test_user["password"]
                expected_role = test_user["expected_role"]
                
                print(f"\n🔍 Testing user: {username}")
                
                # Get user from database
                cur.execute("SELECT username, password, role FROM users WHERE username = %s", (username,))
                user = cur.fetchone()
                
                if user:
                    db_username, db_password, db_role = user
                    
                    # Test password verification
                    if bcrypt.checkpw(password.encode(), db_password.encode()):
                        print(f"✅ Password verification: PASS")
                        
                        # Test role
                        if db_role == expected_role:
                            print(f"✅ Role verification: PASS (role: {db_role})")
                        else:
                            print(f"❌ Role verification: FAIL (expected: {expected_role}, got: {db_role})")
                    else:
                        print(f"❌ Password verification: FAIL")
                else:
                    print(f"❌ User not found in database")
                    
                    # Create missing user
                    print(f"🔧 Creating missing user: {username}")
                    hashed_pw = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
                    
                    cur.execute("""
                        INSERT INTO users (username, password, email, role)
                        VALUES (%s, %s, %s, %s)
                    """, (username, hashed_pw, f"{username}@smartmarket.com", expected_role))
                    
                    conn.commit()
                    print(f"✅ User {username} created successfully")
                    
    except Exception as e:
        print(f"❌ Test failed: {e}")

def test_menu_access():
    """Test menu access for different roles"""
    print("\n🧪 Testing Menu Access by Role")
    print("=" * 50)
    
    # Simulate menu items (from app.py)
    menu_items = {
        'admin': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "⚙️", "label": "Admin Dashboard", "id": "AdminDashboard"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ],
        'user': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "🔔", "label": "Price Alerts", "id": "PriceAlerts"},
            {"icon": "💬", "label": "Feedback", "id": "Feedback"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ],
        'default': [
            {"icon": "📊", "label": "Market Tracker", "id": "MarketTracker"},
            {"icon": "📈", "label": "Analytics", "id": "Analytics"},
            {"icon": "🔔", "label": "Price Alerts", "id": "PriceAlerts"},
            {"icon": "💬", "label": "Feedback", "id": "Feedback"},
            {"icon": "🔐", "label": "Change Password", "id": "ChangePassword"},
            {"icon": "🚪", "label": "Logout", "id": "Logout"}
        ]
    }
    
    roles_to_test = ['admin', 'user', 'other']
    
    for role in roles_to_test:
        print(f"\n🔍 Testing menu access for role: {role}")
        
        # Select menu items based on role (same logic as app.py)
        if role == 'admin':
            items = menu_items['admin']
        elif role == 'user':
            items = menu_items.get('user', menu_items['default'])
        else:
            items = menu_items['default']
        
        print(f"📋 Available menu items:")
        for item in items:
            print(f"   {item['icon']} {item['label']} ({item['id']})")
        
        # Check for admin-only features
        admin_only_items = [item for item in items if item['id'] == 'AdminDashboard']
        if role == 'admin':
            if admin_only_items:
                print("✅ Admin Dashboard access: GRANTED (correct)")
            else:
                print("❌ Admin Dashboard access: DENIED (incorrect)")
        else:
            if not admin_only_items:
                print("✅ Admin Dashboard access: DENIED (correct)")
            else:
                print("❌ Admin Dashboard access: GRANTED (incorrect)")

def main():
    """Main test function"""
    print("🏪 Smart Market User Role Testing")
    print("=" * 50)
    
    try:
        # Test user authentication
        test_user_authentication()
        
        # Test menu access
        test_menu_access()
        
        print("\n" + "=" * 50)
        print("🎉 User role testing complete!")
        print("\n💡 Summary:")
        print("   - Admin users should see Admin Dashboard")
        print("   - Regular users should see Price Alerts and Feedback")
        print("   - Both should have Market Tracker and Analytics")
        print("   - Password verification should work for both")
        
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")

if __name__ == "__main__":
    main()
